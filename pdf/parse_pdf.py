import paddle
import os
import json
import threading  # Add threading for thread-safe initialization
from fastapi import APIRouter, UploadFile
from fastapi.responses import JSONResponse
from huggingface_hub import snapshot_download
from loguru import logger
from magic_pdf.data.data_reader_writer import DataWriter
from magic_pdf.data.dataset import PymuDocDataset
from magic_pdf.model.doc_analyze_by_custom_model import doc_analyze
from magic_pdf.data.data_reader_writer import FileBasedDataWriter
from io import StringIO
import magic_pdf.model as model_config
from dotenv import load_dotenv
from pydantic import BaseModel
from typing import Optional

load_dotenv()

# paddle.set_device(f'gpu:1')


def checkPDFEnabled():
    # Check if PDF parsing is enabled
    if os.getenv("ENABLE_PARSE_PDF", "false").lower() != "true":
        logger.warning(
            "PDF parsing is disabled. Set ENABLE_PARSE_PDF=true to enable it.")
        # 剩下的不执行，但不报错
        return


checkPDFEnabled()

# import paddle
# # paddle.set_device('gpu')  # 确保选择GPU
# paddle.device.set_device("gpu:2")

# Lock for thread-safe initialization
_download_lock = threading.Lock()
_models_initialized = False  # Flag to ensure models are initialized only once

# Function to download required models


def download_models():
    global _models_initialized
    if _models_initialized:
        return  # Skip if already initialized

    with _download_lock:  # Ensure thread-safe initialization
        if _models_initialized:
            return  # Double-check inside the lock

        model_dir = os.getenv("MODEL_DIR", "/opt/models")
        layoutreader_dir = os.getenv("LAYOUTREADER_DIR", "/opt/layoutreader")

        # Check if models already exist and validate contents
        mineru_patterns = [
            "models/Layout/LayoutLMv3/*",
            "models/Layout/YOLO/*",
            "models/MFD/YOLO/*",
            "models/MFR/unimernet_small_2501/*",
            "models/TabRec/TableMaster/*",
            "models/TabRec/StructEqTable/*",
        ]
        all_dirs_exist = all(
            os.path.exists(os.path.join(model_dir, os.path.dirname(pattern))) and
            os.listdir(os.path.join(model_dir, os.path.dirname(pattern)))
            for pattern in mineru_patterns
        )

        if all_dirs_exist:
            print(f"Models already exist in: {model_dir}")
        else:
            snapshot_download(
                "opendatalab/PDF-Extract-Kit-1.0",
                allow_patterns=mineru_patterns,
                local_dir=model_dir,
            )
            print(f"Models downloaded to: {model_dir}")

        # Check if LayoutReader models already exist
        if os.path.exists(layoutreader_dir) and os.listdir(layoutreader_dir):
            print(f"LayoutReader models already exist in: {layoutreader_dir}")
        else:
            layoutreader_pattern = [
                "*.json",
                "*.safetensors",
            ]
            snapshot_download(
                "hantian/layoutreader",
                allow_patterns=layoutreader_pattern,
                local_dir=layoutreader_dir,
            )
            print(f"LayoutReader models downloaded to: {layoutreader_dir}")

        _models_initialized = True  # Mark as initialized


download_models()

model_config.__use_inside_model__ = True


class MemoryDataWriter(DataWriter):
    def __init__(self):
        self.buffer = StringIO()

    def write(self, path: str, data: bytes) -> None:
        if isinstance(data, str):
            self.buffer.write(data)
        else:
            self.buffer.write(data.decode("utf-8"))

    def write_string(self, path: str, data: str) -> None:
        self.buffer.write(data)

    def get_value(self) -> str:
        return self.buffer.getvalue()

    def close(self):
        self.buffer.close()


router = APIRouter()


class PDFPathInput(BaseModel):
    pdf_path: str


class PDFFileInput(BaseModel):
    pdf_file: UploadFile


@router.post(
    "/path",
    tags=["pdf"],
    summary="Parse PDF files from a local path",
)
async def parse_pdf_path(input_data: PDFPathInput):
    """
    Parse a PDF file from a local path and output results to a specified directory.
    """
    # Initialize variables to avoid uninitialized usage.
    content_list_writer = None
    md_content_writer = None
    middle_json_writer = None
    writer = None
    image_writer = None
    pdf_bytes = None

    try:
        # Get output directory from environment variable.
        output_dir = os.getenv("PDF_OUTPUT_DIR", "output")

        # Prepare paths for output files and images.
        pdf_name = os.path.basename(input_data.pdf_path).split(".")[0]
        output_path = os.path.join(output_dir, pdf_name)
        output_image_path = os.path.join(output_path, "images")

        try:
            writer = FileBasedDataWriter(output_path)
            image_writer = FileBasedDataWriter(output_image_path)
            os.makedirs(output_image_path, exist_ok=True)
            with open(input_data.pdf_path, "rb") as f:
                pdf_bytes = f.read()
        except FileNotFoundError:
            return JSONResponse(
                content={"error": f"File not found: {input_data.pdf_path}"},
                status_code=404,
            )

        # Process the PDF content using OCR mode.
        ds = PymuDocDataset(pdf_bytes)
        infer_result = ds.apply(doc_analyze, ocr=True)
        pipe_result = infer_result.pipe_ocr_mode(image_writer)

        # Use MemoryDataWriter to collect results.
        content_list_writer = MemoryDataWriter()
        md_content_writer = MemoryDataWriter()
        middle_json_writer = MemoryDataWriter()

        # Dump results to memory writers.
        pipe_result.dump_content_list(content_list_writer, "", "images")
        pipe_result.dump_md(md_content_writer, "", "images")
        pipe_result.dump_middle_json(middle_json_writer, "")

        # Parse results from memory writers.
        content_list = json.loads(content_list_writer.get_value())
        md_content = md_content_writer.get_value()
        middle_json = json.loads(middle_json_writer.get_value())
        model_json = infer_result.get_infer_res()

        # Write results to the output directory.
        writer.write_string(f"{pdf_name}_content_list.json",
                            content_list_writer.get_value())
        writer.write_string(f"{pdf_name}.md", md_content)
        writer.write_string(f"{pdf_name}_middle.json",
                            middle_json_writer.get_value())
        writer.write_string(
            f"{pdf_name}_model.json",
            json.dumps(model_json, indent=4, ensure_ascii=False),
        )

        # Save visualization results (e.g., layout, spans, line sort).
        pipe_result.draw_layout(os.path.join(
            output_path, f"{pdf_name}_layout.pdf"))
        pipe_result.draw_span(os.path.join(
            output_path, f"{pdf_name}_spans.pdf"))
        pipe_result.draw_line_sort(os.path.join(
            output_path, f"{pdf_name}_line_sort.pdf"))
        infer_result.draw_model(os.path.join(
            output_path, f"{pdf_name}_model.pdf"))

        # Build the response data.
        data = {
            "content_list": content_list,
            "md_content": md_content,
            "info": middle_json,
        }

        return JSONResponse(data, status_code=200)

    except Exception as e:
        # Log and return an error response if an exception occurs.
        logger.exception("Error processing PDF")
        return JSONResponse(content={"error": str(e)}, status_code=500)

    finally:
        # Ensure all MemoryDataWriter instances are properly closed.
        if content_list_writer:
            content_list_writer.close()
        if md_content_writer:
            md_content_writer.close()
        if middle_json_writer:
            middle_json_writer.close()


@router.post(
    "/file",
    tags=["pdf"],
    summary="Parse PDF files from an uploaded file",
)
async def parse_pdf_file(input_data: PDFFileInput):
    """
    Parse a PDF file from an uploaded file and output results to a specified directory.
    """
    # Initialize variables to avoid uninitialized usage.
    content_list_writer = None
    md_content_writer = None
    middle_json_writer = None
    writer = None
    image_writer = None
    pdf_bytes = None

    try:
        # Get output directory from environment variable.
        output_dir = os.getenv("PDF_OUTPUT_DIR", "output")

        # Prepare paths for output files and images.
        pdf_name = os.path.basename(input_data.pdf_file.filename).split(".")[0]
        output_path = os.path.join(output_dir, pdf_name)
        output_image_path = os.path.join(output_path, "images")

        try:
            pdf_bytes = input_data.pdf_file.file.read()
            writer = FileBasedDataWriter(output_path)
            image_writer = FileBasedDataWriter(output_image_path)
            os.makedirs(output_image_path, exist_ok=True)
        except Exception as e:
            return JSONResponse(
                content={
                    "error": f"Error reading uploaded file: {str(e)}"},
                status_code=500,
            )

        # Process the PDF content using OCR mode.
        ds = PymuDocDataset(pdf_bytes)
        infer_result = ds.apply(doc_analyze, ocr=True)
        pipe_result = infer_result.pipe_ocr_mode(image_writer)

        # Use MemoryDataWriter to collect results.
        content_list_writer = MemoryDataWriter()
        md_content_writer = MemoryDataWriter()
        middle_json_writer = MemoryDataWriter()

        # Dump results to memory writers.
        pipe_result.dump_content_list(content_list_writer, "", "images")
        pipe_result.dump_md(md_content_writer, "", "images")
        pipe_result.dump_middle_json(middle_json_writer, "")

        # Parse results from memory writers.
        content_list = json.loads(content_list_writer.get_value())
        md_content = md_content_writer.get_value()
        middle_json = json.loads(middle_json_writer.get_value())
        model_json = infer_result.get_infer_res()

        # Write results to the output directory.
        writer.write_string(f"{pdf_name}_content_list.json",
                            content_list_writer.get_value())
        writer.write_string(f"{pdf_name}.md", md_content)
        writer.write_string(f"{pdf_name}_middle.json",
                            middle_json_writer.get_value())
        writer.write_string(
            f"{pdf_name}_model.json",
            json.dumps(model_json, indent=4, ensure_ascii=False),
        )

        # Save visualization results (e.g., layout, spans, line sort).
        pipe_result.draw_layout(os.path.join(
            output_path, f"{pdf_name}_layout.pdf"))
        pipe_result.draw_span(os.path.join(
            output_path, f"{pdf_name}_spans.pdf"))
        pipe_result.draw_line_sort(os.path.join(
            output_path, f"{pdf_name}_line_sort.pdf"))
        infer_result.draw_model(os.path.join(
            output_path, f"{pdf_name}_model.pdf"))

        # Build the response data.
        data = {
            "content_list": content_list,
            "md_content": md_content,
            "info": middle_json,
        }

        return JSONResponse(data, status_code=200)

    except Exception as e:
        # Log and return an error response if an exception occurs.
        logger.exception("Error processing PDF")
        return JSONResponse(content={"error": str(e)}, status_code=500)

    finally:
        # Ensure all MemoryDataWriter instances are properly closed.
        if content_list_writer:
            content_list_writer.close()
        if md_content_writer:
            md_content_writer.close()
        if middle_json_writer:
            middle_json_writer.close()

        # Ensure the UploadFile object is properly closed.
        if input_data.pdf_file:
            await input_data.pdf_file.close()
