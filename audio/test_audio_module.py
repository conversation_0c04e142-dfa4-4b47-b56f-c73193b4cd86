#!/usr/bin/env python3
"""
Test script for the enhanced audio processing module
"""

import os
import sys
import json
import tempfile
from pathlib import Path

# Add the parent directory to the path to import the module
sys.path.append(str(Path(__file__).parent.parent))

# Set environment variables for testing
os.environ["ENABLE_PARSE_AUDIO"] = "true"
os.environ["AUDIO_BATCH_SIZE"] = "5"
os.environ["AUDIO_MAX_FILE_SIZE"] = "50"  # 50MB for testing

def test_configuration():
    """Test configuration management"""
    print("Testing configuration management...")

    try:
        # Test basic imports without external dependencies
        import sys
        sys.path.append('.')

        # Mock GPUtil to avoid dependency issues
        import types
        gputil_mock = types.ModuleType('GPUtil')
        gputil_mock.getGPUs = lambda: []
        sys.modules['GPUtil'] = gputil_mock

        # Mock soundfile
        soundfile_mock = types.ModuleType('soundfile')
        soundfile_mock.read = lambda x: ([], 16000)
        soundfile_mock.info = lambda x: types.SimpleNamespace(
            duration=30.0, samplerate=16000, channels=1, format='WAV'
        )
        sys.modules['soundfile'] = soundfile_mock

        # Mock librosa
        librosa_mock = types.ModuleType('librosa')
        sys.modules['librosa'] = librosa_mock

        # Mock numpy
        numpy_mock = types.ModuleType('numpy')
        sys.modules['numpy'] = numpy_mock

        # Mock modelscope
        modelscope_mock = types.ModuleType('modelscope')
        modelscope_mock.pipelines = types.ModuleType('pipelines')
        modelscope_mock.utils = types.ModuleType('utils')
        modelscope_mock.utils.constant = types.ModuleType('constant')
        modelscope_mock.utils.constant.Tasks = types.ModuleType('Tasks')
        modelscope_mock.utils.constant.Tasks.auto_speech_recognition = 'auto_speech_recognition'
        sys.modules['modelscope'] = modelscope_mock
        sys.modules['modelscope.pipelines'] = modelscope_mock.pipelines
        sys.modules['modelscope.utils'] = modelscope_mock.utils
        sys.modules['modelscope.utils.constant'] = modelscope_mock.utils.constant

        from audio.parse_audio import config, AudioConfig

        print(f"✓ Audio enabled: {config.enabled}")
        print(f"✓ GPU device: {config.gpu_device}")
        print(f"✓ Batch size: {config.batch_size}")
        print(f"✓ Max file size: {config.max_file_size / (1024*1024):.1f}MB")
        print(f"✓ Supported formats: {config.supported_formats}")
        print(f"✓ Temp directory: {config.temp_dir}")

        # Test format validation
        assert config.is_supported_format("test.wav") == True
        assert config.is_supported_format("test.txt") == False
        print("✓ Format validation working")

        return True
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_models():
    """Test Pydantic data models"""
    print("\nTesting data models...")
    
    try:
        from audio.parse_audio import (
            RecognitionRequest, RecognitionResponse, 
            BatchRecognitionRequest, AudioMetadata,
            HealthCheckResponse, MetricsResponse
        )
        from datetime import datetime
        
        # Test RecognitionRequest
        req = RecognitionRequest(input="/path/to/audio.wav")
        assert req.input == "/path/to/audio.wav"
        print("✓ RecognitionRequest model working")
        
        # Test validation
        try:
            RecognitionRequest(input="")
            assert False, "Should have failed validation"
        except ValueError:
            print("✓ Input validation working")
        
        # Test BatchRecognitionRequest
        batch_req = BatchRecognitionRequest(inputs=["/path/1.wav", "/path/2.wav"])
        assert len(batch_req.inputs) == 2
        print("✓ BatchRecognitionRequest model working")
        
        # Test AudioMetadata
        metadata = AudioMetadata(
            filename="test.wav",
            duration=30.5,
            sample_rate=16000,
            channels=1
        )
        assert metadata.filename == "test.wav"
        print("✓ AudioMetadata model working")
        
        # Test HealthCheckResponse
        health = HealthCheckResponse(
            status="healthy",
            timestamp=datetime.now(),
            models_loaded=True,
            gpu_available=True
        )
        assert health.status == "healthy"
        print("✓ HealthCheckResponse model working")
        
        return True
    except Exception as e:
        print(f"✗ Data models test failed: {e}")
        return False

def test_validators():
    """Test audio validation utilities"""
    print("\nTesting validators...")
    
    try:
        from audio.parse_audio import AudioValidator
        
        # Test format validation
        assert AudioValidator.validate_file_format("test.wav") == True
        assert AudioValidator.validate_file_format("test.txt") == False
        print("✓ Format validation working")
        
        # Create a temporary file for size testing
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
            tmp_file.write(b"dummy audio data" * 1000)  # Small file
            tmp_path = tmp_file.name
        
        try:
            # Test size validation
            assert AudioValidator.validate_file_size(tmp_path) == True
            print("✓ Size validation working")
            
            # Test metadata extraction (will fail gracefully for dummy file)
            metadata = AudioValidator.get_audio_metadata(tmp_path)
            assert metadata.filename == os.path.basename(tmp_path)
            print("✓ Metadata extraction working (graceful failure)")
            
        finally:
            os.unlink(tmp_path)
        
        return True
    except Exception as e:
        print(f"✗ Validators test failed: {e}")
        return False

def test_metrics_collector():
    """Test performance metrics collection"""
    print("\nTesting metrics collector...")
    
    try:
        from audio.parse_audio import MetricsCollector
        
        collector = MetricsCollector()
        
        # Record some test metrics
        collector.record_request(True, 1.5)
        collector.record_request(True, 2.0)
        collector.record_request(False, 0.5)
        
        metrics = collector.get_metrics()
        
        assert metrics.total_requests == 3
        assert metrics.successful_requests == 2
        assert metrics.failed_requests == 1
        assert metrics.average_processing_time > 0
        
        print("✓ Metrics collection working")
        print(f"  - Total requests: {metrics.total_requests}")
        print(f"  - Success rate: {metrics.successful_requests/metrics.total_requests*100:.1f}%")
        print(f"  - Average time: {metrics.average_processing_time:.2f}s")
        
        return True
    except Exception as e:
        print(f"✗ Metrics collector test failed: {e}")
        return False

def test_model_manager():
    """Test model manager (without actually loading models)"""
    print("\nTesting model manager...")
    
    try:
        from audio.parse_audio import ModelManager
        
        manager = ModelManager()
        
        # Test model info
        info = manager.get_model_info()
        assert "inference_model" in info
        assert "device" in info
        print("✓ Model info retrieval working")
        
        # Test loaded status
        loaded = manager.is_loaded()
        print(f"✓ Model loaded status: {loaded}")
        
        return True
    except Exception as e:
        print(f"✗ Model manager test failed: {e}")
        return False

def test_audio_service():
    """Test audio service (without actual processing)"""
    print("\nTesting audio service...")
    
    try:
        from audio.parse_audio import AudioService
        
        service = AudioService()
        
        # Test service initialization
        assert service.model_manager is not None
        assert service.validator is not None
        assert service.metrics is not None
        
        print("✓ Audio service initialization working")
        
        return True
    except Exception as e:
        print(f"✗ Audio service test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("Enhanced Audio Module Test Suite")
    print("=" * 60)
    
    tests = [
        test_configuration,
        test_data_models,
        test_validators,
        test_metrics_collector,
        test_model_manager,
        test_audio_service
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The enhanced audio module is ready.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
