import os
import json
import time
import asyncio
from pathlib import Path
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
import threading
import tempfile
import shutil

import GPUtil
import soundfile
import librosa
import numpy as np
from fastapi import APIRouter, HTTPException, UploadFile, File
from fastapi.responses import StreamingResponse, JSONResponse
from loguru import logger
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks
from pydantic import BaseModel, Field, field_validator
from dotenv import load_dotenv

load_dotenv()


# Configuration Management
class AudioConfig:
    """Audio processing configuration management"""

    def __init__(self):
        self.enabled = os.getenv("ENABLE_PARSE_AUDIO", "false").lower() == "true"
        self.gpu_device = self._get_optimal_gpu_device()
        self.batch_size = int(os.getenv("AUDIO_BATCH_SIZE", "10"))
        self.batch_size_s = int(os.getenv("AUDIO_BATCH_SIZE_S", "300"))
        self.batch_size_threshold_s = int(os.getenv("AUDIO_BATCH_SIZE_THRESHOLD_S", "60"))
        self.max_file_size = int(os.getenv("AUDIO_MAX_FILE_SIZE", "100")) * 1024 * 1024  # MB to bytes
        self.supported_formats = ['.wav', '.mp3', '.flac', '.m4a', '.aac', '.ogg', '.wma']
        self.temp_dir = os.getenv("AUDIO_TEMP_DIR", "/tmp/audio_processing")
        self.cache_enabled = os.getenv("AUDIO_CACHE_ENABLED", "true").lower() == "true"
        self.max_concurrent_tasks = int(os.getenv("AUDIO_MAX_CONCURRENT_TASKS", "3"))

        # Ensure temp directory exists
        os.makedirs(self.temp_dir, exist_ok=True)

    def _get_optimal_gpu_device(self) -> str:
        """Automatically select the best available GPU device"""
        try:
            gpus = GPUtil.getGPUs()
            if not gpus:
                logger.warning("No GPUs available, using CPU")
                return "cpu"

            # Select GPU with most free memory
            best_gpu = min(gpus, key=lambda gpu: gpu.memoryUsed)
            device = f"cuda:{best_gpu.id}"
            logger.info(f"Selected GPU device: {device} (Memory: {best_gpu.memoryFree}MB free)")
            return device
        except Exception as e:
            logger.warning(f"Failed to detect GPU: {e}, using CPU")
            return "cpu"

    def is_supported_format(self, filename: str) -> bool:
        """Check if file format is supported"""
        return Path(filename).suffix.lower() in self.supported_formats


def check_audio_enabled():
    """Check if audio parsing is enabled"""
    if not config.enabled:
        logger.warning("Audio parsing is disabled. Set ENABLE_PARSE_AUDIO=true to enable it.")
        return False
    return True


# Initialize configuration
config = AudioConfig()

# Check if audio processing is enabled
if not check_audio_enabled():
    logger.warning("Audio module is disabled")
else:
    logger.info(f"Audio module initialized with device: {config.gpu_device}")

# Data Models and Validators

class AudioMetadata(BaseModel):
    """Audio file metadata"""
    filename: str
    duration: Optional[float] = None
    sample_rate: Optional[int] = None
    channels: Optional[int] = None
    format: Optional[str] = None
    size_bytes: Optional[int] = None
    bitrate: Optional[int] = None


class RecognitionRequest(BaseModel):
    """Speech recognition request"""
    input: str = Field(..., description="Path to audio file or audio data")

    @field_validator('input')
    @classmethod
    def validate_input(cls, v):
        if not v or not v.strip():
            raise ValueError("Input cannot be empty")
        return v.strip()


class RecognitionResponse(BaseModel):
    """Speech recognition response"""
    output: Any = Field(..., description="Recognition result")
    metadata: Optional[AudioMetadata] = None
    processing_time: Optional[float] = None
    model_info: Optional[Dict[str, str]] = None


class BatchRecognitionRequest(BaseModel):
    """Batch speech recognition request"""
    inputs: List[str] = Field(..., min_items=1, max_items=10, description="List of audio file paths")

    @field_validator('inputs')
    @classmethod
    def validate_inputs(cls, v):
        if not v:
            raise ValueError("Inputs list cannot be empty")
        for input_path in v:
            if not input_path or not input_path.strip():
                raise ValueError("Input path cannot be empty")
        return [path.strip() for path in v]


class BatchRecognitionResponse(BaseModel):
    """Batch speech recognition response"""
    results: List[RecognitionResponse]
    total_count: int
    success_count: int
    failed_count: int
    processing_time: float


class StreamingRecognitionResponse(BaseModel):
    """Streaming recognition response"""
    key: str
    text: str
    timestamp: Optional[float] = None
    confidence: Optional[float] = None


class AudioUploadResponse(BaseModel):
    """Audio file upload response"""
    filename: str
    file_path: str
    metadata: AudioMetadata
    message: str = "File uploaded successfully"


class HealthCheckResponse(BaseModel):
    """Health check response"""
    status: str
    timestamp: datetime
    models_loaded: bool
    gpu_available: bool
    gpu_memory_free: Optional[float] = None
    version: str = "1.0.0"


class MetricsResponse(BaseModel):
    """Performance metrics response"""
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_processing_time: float
    gpu_utilization: Optional[float] = None
    memory_usage: Optional[float] = None


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str
    detail: Optional[str] = None
    timestamp: datetime
    request_id: Optional[str] = None


# Audio Processing Utilities

class AudioValidator:
    """Audio file validation utilities"""

    @staticmethod
    def validate_file_format(file_path: str) -> bool:
        """Validate if file format is supported"""
        return config.is_supported_format(file_path)

    @staticmethod
    def validate_file_size(file_path: str) -> bool:
        """Validate if file size is within limits"""
        try:
            size = os.path.getsize(file_path)
            return size <= config.max_file_size
        except OSError:
            return False

    @staticmethod
    def get_audio_metadata(file_path: str) -> AudioMetadata:
        """Extract audio metadata"""
        try:
            info = soundfile.info(file_path)
            file_size = os.path.getsize(file_path)

            return AudioMetadata(
                filename=os.path.basename(file_path),
                duration=info.duration,
                sample_rate=info.samplerate,
                channels=info.channels,
                format=info.format,
                size_bytes=file_size,
                bitrate=int(file_size * 8 / info.duration) if info.duration > 0 else None
            )
        except Exception as e:
            logger.error(f"Failed to extract metadata from {file_path}: {e}")
            return AudioMetadata(filename=os.path.basename(file_path))


# Performance Metrics Collector

class MetricsCollector:
    """Collect and manage performance metrics"""

    def __init__(self):
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.processing_times = []
        self._lock = threading.Lock()

    def record_request(self, success: bool, processing_time: float):
        """Record a request result"""
        with self._lock:
            self.total_requests += 1
            if success:
                self.successful_requests += 1
            else:
                self.failed_requests += 1
            self.processing_times.append(processing_time)

            # Keep only last 1000 processing times
            if len(self.processing_times) > 1000:
                self.processing_times = self.processing_times[-1000:]

    def get_metrics(self) -> MetricsResponse:
        """Get current metrics"""
        with self._lock:
            avg_time = sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0

            # Get GPU utilization if available
            gpu_util = None
            try:
                gpus = GPUtil.getGPUs()
                if gpus and config.gpu_device.startswith("cuda"):
                    gpu_id = int(config.gpu_device.split(":")[1])
                    if gpu_id < len(gpus):
                        gpu_util = gpus[gpu_id].load * 100
            except Exception:
                pass

            return MetricsResponse(
                total_requests=self.total_requests,
                successful_requests=self.successful_requests,
                failed_requests=self.failed_requests,
                average_processing_time=avg_time,
                gpu_utilization=gpu_util
            )


# Global metrics collector
metrics_collector = MetricsCollector()


# Error handling decorator
def handle_audio_errors(func):
    """Decorator for consistent error handling"""
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except FileNotFoundError as e:
            logger.error(f"File not found in {func.__name__}: {e}")
            raise HTTPException(status_code=404, detail="Audio file not found")
        except PermissionError as e:
            logger.error(f"Permission error in {func.__name__}: {e}")
            raise HTTPException(status_code=403, detail="Permission denied")
        except ValueError as e:
            logger.error(f"Validation error in {func.__name__}: {e}")
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            logger.exception(f"Unexpected error in {func.__name__}: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
    return wrapper

# Model Management

class ModelManager:
    """Manage audio processing models"""

    def __init__(self):
        self._inference_pipeline = None
        self._streaming_pipeline = None
        self._pipeline_lock = threading.Lock()
        self._model_info = {
            "inference_model": "iic/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
            "streaming_model": "iic/speech_paraformer_asr_nat-zh-cn-16k-common-vocab8404-online",
            "device": config.gpu_device,
            "loaded": False
        }

    def get_inference_pipeline(self):
        """Get or initialize inference pipeline"""
        if self._inference_pipeline is not None:
            return self._inference_pipeline

        with self._pipeline_lock:
            if self._inference_pipeline is not None:
                return self._inference_pipeline

            try:
                logger.info("Initializing inference pipeline...")
                self._inference_pipeline = pipeline(
                    task=Tasks.auto_speech_recognition,
                    model='iic/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch',
                    vad_model='iic/speech_fsmn_vad_zh-cn-16k-common-pytorch',
                    vad_kwargs={"max_single_segment_time": 60000},
                    punc_model='iic/punc_ct-transformer_cn-en-common-vocab471067-large',
                    lm_model='iic/speech_transformer_lm_zh-cn-common-vocab8404-pytorch',
                    device=config.gpu_device,
                    batch_size=config.batch_size,
                    batch_size_s=config.batch_size_s,
                    batch_size_threshold_s=config.batch_size_threshold_s,
                    return_raw_text=True,
                    sentence_timestamp=True,
                    return_spk_res=False,
                    disable_update=True,
                )
                self._model_info["loaded"] = True
                logger.info("Inference pipeline initialized successfully")
                return self._inference_pipeline
            except Exception as e:
                logger.error(f"Failed to initialize inference pipeline: {e}")
                raise HTTPException(status_code=500, detail=f"Model initialization failed: {str(e)}")

    def get_streaming_pipeline(self):
        """Get or initialize streaming pipeline"""
        if self._streaming_pipeline is not None:
            return self._streaming_pipeline

        with self._pipeline_lock:
            if self._streaming_pipeline is not None:
                return self._streaming_pipeline

            try:
                logger.info("Initializing streaming pipeline...")
                self._streaming_pipeline = pipeline(
                    task=Tasks.auto_speech_recognition,
                    model='iic/speech_paraformer_asr_nat-zh-cn-16k-common-vocab8404-online',
                    punc_model='iic/punc_ct-transformer_cn-en-common-vocab471067-large',
                    device=config.gpu_device,
                    model_revision='v2.0.4',
                    disable_update=True,
                )
                logger.info("Streaming pipeline initialized successfully")
                return self._streaming_pipeline
            except Exception as e:
                logger.error(f"Failed to initialize streaming pipeline: {e}")
                raise HTTPException(status_code=500, detail=f"Streaming model initialization failed: {str(e)}")

    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        return self._model_info.copy()

    def is_loaded(self) -> bool:
        """Check if models are loaded"""
        return self._model_info["loaded"]


# Global model manager
model_manager = ModelManager()

# Legacy function for backward compatibility
def get_inference_pipeline():
    """Legacy function for backward compatibility"""
    return model_manager.get_inference_pipeline()


# Audio Processing Service

class AudioService:
    """Core audio processing service"""

    def __init__(self):
        self.model_manager = model_manager
        self.validator = AudioValidator()
        self.metrics = metrics_collector

    async def process_audio(self, file_path: str, extract_metadata: bool = True) -> RecognitionResponse:
        """Process single audio file"""
        start_time = time.time()
        success = False

        try:
            # Validate file
            if not self.validator.validate_file_format(file_path):
                raise HTTPException(status_code=400, detail="Unsupported audio format")

            if not self.validator.validate_file_size(file_path):
                raise HTTPException(status_code=400, detail="File size exceeds limit")

            # Get pipeline and process
            pipeline = self.model_manager.get_inference_pipeline()
            result = pipeline(file_path)

            # Extract metadata if requested
            metadata = None
            if extract_metadata:
                metadata = self.validator.get_audio_metadata(file_path)

            processing_time = time.time() - start_time
            success = True

            return RecognitionResponse(
                output=result,
                metadata=metadata,
                processing_time=processing_time,
                model_info=self.model_manager.get_model_info()
            )

        except Exception as e:
            logger.error(f"Error processing audio {file_path}: {e}")
            raise HTTPException(status_code=500, detail=str(e))
        finally:
            processing_time = time.time() - start_time
            self.metrics.record_request(success, processing_time)

    async def process_batch(self, file_paths: List[str]) -> BatchRecognitionResponse:
        """Process multiple audio files"""
        start_time = time.time()
        results = []
        success_count = 0
        failed_count = 0

        for file_path in file_paths:
            try:
                result = await self.process_audio(file_path, extract_metadata=True)
                results.append(result)
                success_count += 1
            except Exception as e:
                logger.error(f"Failed to process {file_path}: {e}")
                # Add error result
                error_result = RecognitionResponse(
                    output={"error": str(e)},
                    metadata=AudioMetadata(filename=os.path.basename(file_path)),
                    processing_time=0
                )
                results.append(error_result)
                failed_count += 1

        total_time = time.time() - start_time

        return BatchRecognitionResponse(
            results=results,
            total_count=len(file_paths),
            success_count=success_count,
            failed_count=failed_count,
            processing_time=total_time
        )


# Global service instance
audio_service = AudioService()

# FastAPI Router
router = APIRouter()

# API Endpoints

@router.post(
    "",
    response_model=RecognitionResponse,
    tags=["audio"],
    summary="Parse audio files and perform speech recognition",
    description="Process a single audio file and return speech recognition results"
)
async def parse_audio(request: RecognitionRequest) -> RecognitionResponse:
    """Legacy endpoint for backward compatibility"""
    logger.info("Processing audio recognition request: {}", request.input)
    return await audio_service.process_audio(request.input)


async def stream_recognition_v2(request: RecognitionRequest):
    """Streaming recognition generator"""
    logger.info("Starting streaming recognition for: {}", request.input)

    try:
        # Validate file
        if not audio_service.validator.validate_file_format(request.input):
            raise HTTPException(status_code=400, detail="Unsupported audio format")

        if not audio_service.validator.validate_file_size(request.input):
            raise HTTPException(status_code=400, detail="File size exceeds limit")

        # Read audio file
        speech, sample_rate = soundfile.read(request.input)
        speech_length = speech.shape[0]

        # Get streaming pipeline
        streaming_pipeline = model_manager.get_streaming_pipeline()

        # Streaming parameters
        sample_offset = 0
        chunk_size = [5, 10, 5]
        encoder_chunk_look_back = 4
        decoder_chunk_look_back = 1
        stride_size = chunk_size[1] * 960
        cache = {}
        is_final = False

        for sample_offset in range(0, speech_length, min(stride_size, speech_length - sample_offset)):
            if sample_offset + stride_size >= speech_length - 1:
                stride_size = speech_length - sample_offset
                is_final = True

            res = streaming_pipeline(
                speech[sample_offset: sample_offset + stride_size],
                cache=cache,
                is_final=is_final,
                encoder_chunk_look_back=encoder_chunk_look_back,
                decoder_chunk_look_back=decoder_chunk_look_back
            )

            if len(res) == 0:
                break

            # Format response
            response_data = {
                "key": f"chunk_{sample_offset}",
                "text": res[0]["text"] if res and len(res) > 0 else "",
                "timestamp": sample_offset / sample_rate if sample_rate > 0 else 0,
                "is_final": is_final
            }

            logger.debug("Streaming result: {}", response_data)
            yield f"data: {json.dumps(response_data)}\n\n"

    except Exception as e:
        logger.error(f"Streaming recognition error: {e}")
        error_data = {
            "error": str(e),
            "timestamp": time.time()
        }
        yield f"data: {json.dumps(error_data)}\n\n"


@router.post(
    "/streaming",
    tags=["audio"],
    summary="Perform streaming speech recognition",
    description="Process audio file with streaming recognition for real-time results"
)
async def streaming_recognition_v2(request: RecognitionRequest) -> StreamingResponse:
    """Streaming recognition endpoint"""
    return StreamingResponse(
        stream_recognition_v2(request),
        media_type="text/plain",
        headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
    )


@router.post(
    "/upload",
    response_model=AudioUploadResponse,
    tags=["audio"],
    summary="Upload audio file for processing",
    description="Upload an audio file and get metadata information"
)
async def upload_audio_file(
    file: UploadFile = File(..., description="Audio file to upload")
) -> AudioUploadResponse:
    """Upload audio file endpoint"""

    # Validate file format
    if not config.is_supported_format(file.filename):
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file format. Supported formats: {', '.join(config.supported_formats)}"
        )

    # Check file size
    if file.size and file.size > config.max_file_size:
        raise HTTPException(
            status_code=400,
            detail=f"File size exceeds limit of {config.max_file_size / (1024*1024):.1f}MB"
        )

    try:
        # Create unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{file.filename}"
        file_path = os.path.join(config.temp_dir, filename)

        # Save uploaded file
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # Extract metadata
        metadata = AudioValidator.get_audio_metadata(file_path)

        logger.info(f"Audio file uploaded successfully: {filename}")

        return AudioUploadResponse(
            filename=filename,
            file_path=file_path,
            metadata=metadata
        )

    except Exception as e:
        logger.error(f"Failed to upload audio file: {e}")
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


@router.post(
    "/batch",
    response_model=BatchRecognitionResponse,
    tags=["audio"],
    summary="Batch audio processing",
    description="Process multiple audio files in a single request"
)
async def batch_recognition(request: BatchRecognitionRequest) -> BatchRecognitionResponse:
    """Batch processing endpoint"""
    logger.info(f"Processing batch request with {len(request.inputs)} files")

    if len(request.inputs) > config.max_concurrent_tasks:
        raise HTTPException(
            status_code=400,
            detail=f"Too many files. Maximum allowed: {config.max_concurrent_tasks}"
        )

    return await audio_service.process_batch(request.inputs)


@router.get(
    "/health",
    response_model=HealthCheckResponse,
    tags=["audio"],
    summary="Health check",
    description="Check the health status of the audio processing service"
)
async def health_check() -> HealthCheckResponse:
    """Health check endpoint"""

    # Check GPU availability
    gpu_available = False
    gpu_memory_free = None

    try:
        if config.gpu_device.startswith("cuda"):
            gpus = GPUtil.getGPUs()
            if gpus:
                gpu_id = int(config.gpu_device.split(":")[1])
                if gpu_id < len(gpus):
                    gpu_available = True
                    gpu_memory_free = gpus[gpu_id].memoryFree
    except Exception as e:
        logger.warning(f"Failed to check GPU status: {e}")

    return HealthCheckResponse(
        status="healthy" if config.enabled else "disabled",
        timestamp=datetime.now(),
        models_loaded=model_manager.is_loaded(),
        gpu_available=gpu_available,
        gpu_memory_free=gpu_memory_free
    )


@router.get(
    "/metrics",
    response_model=MetricsResponse,
    tags=["audio"],
    summary="Performance metrics",
    description="Get performance metrics and statistics"
)
async def get_metrics() -> MetricsResponse:
    """Get performance metrics"""
    return metrics_collector.get_metrics()


@router.get(
    "/models/status",
    tags=["audio"],
    summary="Model status",
    description="Get information about loaded models"
)
async def get_model_status() -> Dict[str, Any]:
    """Get model status information"""
    return {
        "model_info": model_manager.get_model_info(),
        "config": {
            "device": config.gpu_device,
            "batch_size": config.batch_size,
            "supported_formats": config.supported_formats,
            "max_file_size_mb": config.max_file_size / (1024 * 1024)
        },
        "status": "loaded" if model_manager.is_loaded() else "not_loaded"
    }


@router.post(
    "/validate",
    tags=["audio"],
    summary="Validate audio file",
    description="Validate audio file format and extract metadata without processing"
)
async def validate_audio_file(request: RecognitionRequest) -> Dict[str, Any]:
    """Validate audio file endpoint"""

    try:
        # Check if file exists
        if not os.path.exists(request.input):
            raise HTTPException(status_code=404, detail="Audio file not found")

        # Validate format
        format_valid = AudioValidator.validate_file_format(request.input)
        size_valid = AudioValidator.validate_file_size(request.input)

        # Get metadata
        metadata = AudioValidator.get_audio_metadata(request.input)

        return {
            "file_path": request.input,
            "format_valid": format_valid,
            "size_valid": size_valid,
            "metadata": metadata.model_dump(),
            "supported_formats": config.supported_formats,
            "max_file_size_mb": config.max_file_size / (1024 * 1024)
        }

    except Exception as e:
        logger.error(f"Validation error for {request.input}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
