# Audio Processing Module

Enhanced audio processing module for OmniParse with comprehensive speech recognition capabilities.

## Features

### Core Functionality
- **Speech Recognition**: Convert audio files to text using advanced ASR models
- **Streaming Recognition**: Real-time speech recognition with streaming results
- **Batch Processing**: Process multiple audio files simultaneously
- **File Upload**: Upload audio files with validation and metadata extraction
- **Format Support**: Support for multiple audio formats (WAV, MP3, FLAC, M4A, AAC, OGG, WMA)

### Production Features
- **Health Monitoring**: Health check endpoints for service monitoring
- **Performance Metrics**: Detailed performance statistics and GPU utilization
- **Error Handling**: Comprehensive error handling with detailed logging
- **Resource Management**: Automatic GPU selection and memory management
- **Configuration Management**: Environment-based configuration
- **Input Validation**: File format, size, and content validation

## API Endpoints

### Core Recognition APIs (Backward Compatible)

#### POST `/v1/parse/audio`
Process a single audio file for speech recognition.

**Request:**
```json
{
  "input": "/path/to/audio/file.wav"
}
```

**Response:**
```json
{
  "output": "Recognized speech text...",
  "metadata": {
    "filename": "file.wav",
    "duration": 30.5,
    "sample_rate": 16000,
    "channels": 1,
    "format": "WAV",
    "size_bytes": 976000,
    "bitrate": 256000
  },
  "processing_time": 2.34,
  "model_info": {
    "inference_model": "iic/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
    "device": "cuda:0",
    "loaded": true
  }
}
```

#### POST `/v1/parse/audio/streaming`
Streaming speech recognition for real-time results.

**Request:**
```json
{
  "input": "/path/to/audio/file.wav"
}
```

**Response:** Server-Sent Events stream
```
data: {"key": "chunk_0", "text": "Hello", "timestamp": 0.0, "is_final": false}
data: {"key": "chunk_960", "text": "Hello world", "timestamp": 0.06, "is_final": false}
data: {"key": "chunk_1920", "text": "Hello world!", "timestamp": 0.12, "is_final": true}
```

### New Enhanced APIs

#### POST `/v1/parse/audio/upload`
Upload an audio file with validation and metadata extraction.

**Request:** Multipart form data with audio file

**Response:**
```json
{
  "filename": "20241204_143022_audio.wav",
  "file_path": "/tmp/audio_processing/20241204_143022_audio.wav",
  "metadata": {
    "filename": "audio.wav",
    "duration": 30.5,
    "sample_rate": 16000,
    "channels": 1,
    "format": "WAV",
    "size_bytes": 976000,
    "bitrate": 256000
  },
  "message": "File uploaded successfully"
}
```

#### POST `/v1/parse/audio/batch`
Process multiple audio files in a single request.

**Request:**
```json
{
  "inputs": [
    "/path/to/file1.wav",
    "/path/to/file2.mp3",
    "/path/to/file3.flac"
  ]
}
```

**Response:**
```json
{
  "results": [
    {
      "output": "First audio transcription...",
      "metadata": {...},
      "processing_time": 1.23
    },
    {
      "output": "Second audio transcription...",
      "metadata": {...},
      "processing_time": 2.45
    }
  ],
  "total_count": 3,
  "success_count": 2,
  "failed_count": 1,
  "processing_time": 5.67
}
```

#### POST `/v1/parse/audio/validate`
Validate audio file and extract metadata without processing.

**Request:**
```json
{
  "input": "/path/to/audio/file.wav"
}
```

**Response:**
```json
{
  "file_path": "/path/to/audio/file.wav",
  "format_valid": true,
  "size_valid": true,
  "metadata": {...},
  "supported_formats": [".wav", ".mp3", ".flac", ".m4a", ".aac", ".ogg", ".wma"],
  "max_file_size_mb": 100.0
}
```

### Monitoring APIs

#### GET `/v1/parse/audio/health`
Health check endpoint for service monitoring.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-12-04T14:30:22.123456",
  "models_loaded": true,
  "gpu_available": true,
  "gpu_memory_free": 8192.0,
  "version": "1.0.0"
}
```

#### GET `/v1/parse/audio/metrics`
Performance metrics and statistics.

**Response:**
```json
{
  "total_requests": 1250,
  "successful_requests": 1200,
  "failed_requests": 50,
  "average_processing_time": 2.34,
  "gpu_utilization": 45.6,
  "memory_usage": null
}
```

#### GET `/v1/parse/audio/models/status`
Model status and configuration information.

**Response:**
```json
{
  "model_info": {
    "inference_model": "iic/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
    "streaming_model": "iic/speech_paraformer_asr_nat-zh-cn-16k-common-vocab8404-online",
    "device": "cuda:0",
    "loaded": true
  },
  "config": {
    "device": "cuda:0",
    "batch_size": 10,
    "supported_formats": [".wav", ".mp3", ".flac", ".m4a", ".aac", ".ogg", ".wma"],
    "max_file_size_mb": 100.0
  },
  "status": "loaded"
}
```

## Configuration

Configure the audio module using environment variables:

```bash
# Enable/disable audio processing
ENABLE_PARSE_AUDIO=true

# Processing configuration
AUDIO_BATCH_SIZE=10
AUDIO_BATCH_SIZE_S=300
AUDIO_BATCH_SIZE_THRESHOLD_S=60
AUDIO_MAX_FILE_SIZE=100  # MB
AUDIO_MAX_CONCURRENT_TASKS=3

# Storage configuration
AUDIO_TEMP_DIR=/tmp/audio_processing
AUDIO_CACHE_ENABLED=true
```

## Installation

1. Install dependencies:
```bash
pip install -r audio/requirements.txt
```

2. Set environment variables:
```bash
export ENABLE_PARSE_AUDIO=true
```

3. Start the service:
```bash
python app.py
```

## Supported Audio Formats

- WAV (.wav)
- MP3 (.mp3)
- FLAC (.flac)
- M4A (.m4a)
- AAC (.aac)
- OGG (.ogg)
- WMA (.wma)

## Error Handling

The module provides comprehensive error handling with appropriate HTTP status codes:

- `400 Bad Request`: Invalid input, unsupported format, file too large
- `404 Not Found`: Audio file not found
- `403 Forbidden`: Permission denied
- `500 Internal Server Error`: Processing errors, model initialization failures

## Performance Considerations

- **GPU Acceleration**: Automatically selects the best available GPU
- **Batch Processing**: Efficient processing of multiple files
- **Memory Management**: Automatic resource cleanup and monitoring
- **Caching**: Optional caching for improved performance
- **Concurrent Processing**: Configurable concurrent task limits

## Backward Compatibility

All existing APIs remain unchanged to ensure production compatibility:
- `POST /v1/parse/audio` - Original recognition endpoint
- `POST /v1/parse/audio/streaming` - Original streaming endpoint

New features are added as additional endpoints without breaking existing functionality.
