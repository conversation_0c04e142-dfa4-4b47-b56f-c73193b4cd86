# 短剧视频分析剪辑软件 - 系统架构设计

## 1. 系统架构概览

### 1.1 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        FE[Vue3 + TailwindCSS]
        FE --> |HTTP/WebSocket| API
    end
    
    subgraph "API网关层"
        API[FastAPI Gateway]
        API --> |路由分发| BS[业务服务]
    end
    
    subgraph "业务逻辑层"
        BS --> TS[任务服务]
        BS --> VS[视频分析服务]
        BS --> CS[片段剪辑服务]
        BS --> AS[AI推理服务]
    end
    
    subgraph "数据层"
        DB[(SQLite3)]
        REDIS[(Redis)]
        FS[文件存储]
    end
    
    subgraph "任务处理层"
        CELERY[Celery Workers]
        CELERY --> |消费任务| REDIS
        CELERY --> |AI处理| AS
    end
    
    BS --> DB
    BS --> REDIS
    BS --> FS
```

### 1.2 技术栈详细说明

#### 前端技术栈
- **框架**: Vue 3.4+ (Composition API)
- **构建工具**: Vite 5.0+
- **样式框架**: TailwindCSS 3.4+
- **状态管理**: Pinia 2.1+
- **路由**: Vue Router 4.2+
- **HTTP客户端**: Axios 1.6+
- **UI组件**: 自定义组件 + Headless UI
- **视频播放**: Video.js
- **图表库**: Chart.js / ECharts
- **文件上传**: vue-upload-component

#### 后端技术栈
- **语言**: Python 3.10+
- **Web框架**: FastAPI 0.104+
- **ORM**: SQLAlchemy 2.0+
- **数据库**: SQLite3
- **缓存**: Redis 7.0+
- **任务队列**: Celery 5.3+
- **AI推理**: 
  - OpenCV 4.8+ (视频处理)
  - MediaPipe (人脸识别)
  - Whisper (语音识别)
  - Transformers (NLP)
- **文件处理**: FFmpeg-python
- **异步**: asyncio + uvloop

## 2. 项目目录结构

```
video/
├── frontend/                 # 前端项目
│   ├── src/
│   │   ├── components/      # 通用组件
│   │   ├── views/          # 页面组件
│   │   ├── stores/         # Pinia状态管理
│   │   ├── router/         # 路由配置
│   │   ├── api/            # API接口
│   │   ├── utils/          # 工具函数
│   │   └── assets/         # 静态资源
│   ├── public/
│   ├── package.json
│   ├── vite.config.js
│   └── tailwind.config.js
├── backend/                 # 后端项目
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务服务
│   │   ├── tasks/          # Celery任务
│   │   ├── utils/          # 工具函数
│   │   └── main.py         # 应用入口
│   ├── alembic/            # 数据库迁移
│   ├── requirements.txt
│   └── pyproject.toml
├── storage/                 # 文件存储
│   ├── uploads/            # 上传文件
│   ├── processed/          # 处理后文件
│   ├── thumbnails/         # 缩略图
│   └── exports/            # 导出文件
├── docker/                 # Docker配置
├── docs/                   # 文档
└── scripts/                # 脚本文件
```

## 3. 前端架构设计

### 3.1 页面组件结构

```
src/views/
├── TaskManagement/         # 任务管理
│   ├── TaskList.vue
│   ├── TaskCard.vue
│   └── CreateTask.vue
├── VideoAnalysis/          # 视频分析
│   ├── AnalysisWorkspace.vue
│   ├── VideoPlayer.vue
│   ├── AnalysisSteps.vue
│   └── ResultPanel.vue
├── ClipManagement/         # 片段管理
│   ├── ClipGrid.vue
│   ├── ClipCard.vue
│   └── FilterPanel.vue
└── EditingWorkspace/       # 剪辑工作台
    ├── Timeline.vue
    ├── PreviewPlayer.vue
    ├── AssetLibrary.vue
    └── ToolPanel.vue
```

### 3.2 状态管理设计

```javascript
// stores/
├── auth.js          # 用户认证
├── tasks.js         # 任务管理
├── videos.js        # 视频数据
├── analysis.js      # 分析结果
├── clips.js         # 片段数据
└── editor.js        # 编辑器状态
```

### 3.3 API接口设计

```javascript
// api/
├── auth.js          # 认证接口
├── tasks.js         # 任务接口
├── videos.js        # 视频接口
├── analysis.js      # 分析接口
├── clips.js         # 片段接口
└── export.js        # 导出接口
```

## 4. 后端架构设计

### 4.1 API路由结构

```
app/api/
├── auth/            # 认证相关
├── tasks/           # 任务管理
├── videos/          # 视频处理
├── analysis/        # 分析服务
├── clips/           # 片段管理
└── export/          # 导出服务
```

### 4.2 服务层设计

```
app/services/
├── task_service.py      # 任务管理服务
├── video_service.py     # 视频处理服务
├── analysis_service.py  # 分析服务
├── clip_service.py      # 片段服务
├── ai_service.py        # AI推理服务
└── export_service.py    # 导出服务
```

### 4.3 数据模型设计

```python
# 主要数据模型
- Task: 任务信息
- Video: 视频文件信息
- AnalysisResult: 分析结果
- Clip: 视频片段
- Project: 剪辑项目
- User: 用户信息
```

## 5. 数据库设计

### 5.1 核心表结构

```sql
-- 任务表
CREATE TABLE tasks (
    id INTEGER PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    config JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 视频表
CREATE TABLE videos (
    id INTEGER PRIMARY KEY,
    task_id INTEGER REFERENCES tasks(id),
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    duration FLOAT,
    resolution VARCHAR(20),
    fps FLOAT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 分析结果表
CREATE TABLE analysis_results (
    id INTEGER PRIMARY KEY,
    video_id INTEGER REFERENCES videos(id),
    step VARCHAR(50) NOT NULL,
    result JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 片段表
CREATE TABLE clips (
    id INTEGER PRIMARY KEY,
    video_id INTEGER REFERENCES videos(id),
    start_time FLOAT NOT NULL,
    end_time FLOAT NOT NULL,
    clip_type VARCHAR(50),
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 6. API设计规范

### 6.1 RESTful API设计

```
# 任务管理
GET    /api/v1/tasks              # 获取任务列表
POST   /api/v1/tasks              # 创建新任务
GET    /api/v1/tasks/{id}         # 获取任务详情
PUT    /api/v1/tasks/{id}         # 更新任务
DELETE /api/v1/tasks/{id}         # 删除任务

# 视频管理
POST   /api/v1/videos/upload      # 上传视频
GET    /api/v1/videos/{id}        # 获取视频信息
POST   /api/v1/videos/{id}/analyze # 开始分析

# 分析结果
GET    /api/v1/analysis/{video_id} # 获取分析结果
GET    /api/v1/analysis/{video_id}/step/{step} # 获取特定步骤结果

# 片段管理
GET    /api/v1/clips              # 获取片段列表
POST   /api/v1/clips/generate     # 生成片段
GET    /api/v1/clips/{id}         # 获取片段详情
```

### 6.2 WebSocket接口

```
# 实时通信
/ws/task/{task_id}               # 任务进度推送
/ws/analysis/{video_id}          # 分析进度推送
```

## 7. 部署架构

### 7.1 开发环境
- 前端: Vite Dev Server (端口3000)
- 后端: FastAPI + Uvicorn (端口8000)
- Redis: 本地Redis服务 (端口6379)
- Celery: 本地Worker进程

### 7.2 生产环境
- 前端: Nginx静态文件服务
- 后端: Gunicorn + Uvicorn Workers
- 负载均衡: Nginx反向代理
- 任务队列: Redis Cluster + Celery Workers
- 监控: Prometheus + Grafana

## 8. 开发环境配置

### 8.1 前端环境配置
```bash
cd frontend
npm install
npm run dev
```

### 8.2 后端环境配置
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --port 8000
```

### 8.3 Redis和Celery配置
```bash
# 启动Redis
redis-server

# 启动Celery Worker
celery -A app.tasks.celery worker --loglevel=info
```
