"""
File organization utilities for standardized video storage structure
"""

import os
import shutil
from pathlib import Path
from typing import Dict, List, Optional
from loguru import logger


class VideoFileOrganizer:
    """Manages standardized file organization for video processing system"""
    
    def __init__(self, base_storage_path: str = "../storage/videos"):
        self.base_storage_path = Path(base_storage_path)
        self.base_storage_path.mkdir(parents=True, exist_ok=True)
    
    def get_video_directory(self, video_id: int) -> Path:
        """Get the standardized directory path for a video"""
        return self.base_storage_path / str(video_id)
    
    def get_video_subdirectory(self, video_id: int, subdir: str) -> Path:
        """Get a subdirectory path for a video"""
        valid_subdirs = ['audios', 'frames', 'subtitles', 'thumbnails']
        if subdir not in valid_subdirs:
            raise ValueError(f"Invalid subdirectory: {subdir}. Must be one of {valid_subdirs}")
        
        subdir_path = self.get_video_directory(video_id) / subdir
        subdir_path.mkdir(parents=True, exist_ok=True)
        return subdir_path
    
    def create_video_directory_structure(self, video_id: int) -> Dict[str, Path]:
        """Create the complete directory structure for a video"""
        video_dir = self.get_video_directory(video_id)
        video_dir.mkdir(parents=True, exist_ok=True)
        
        subdirs = {}
        for subdir in ['audios', 'frames', 'subtitles', 'thumbnails']:
            subdirs[subdir] = self.get_video_subdirectory(video_id, subdir)
        
        logger.info(f"Created directory structure for video {video_id} at {video_dir}")
        return {
            'root': video_dir,
            **subdirs
        }
    
    def get_original_video_path(self, video_id: int, filename: str) -> Path:
        """Get the path where the original video file should be stored"""
        return self.get_video_directory(video_id) / filename
    
    def get_audio_file_path(self, video_id: int, stream_index: int, codec: str = "wav") -> Path:
        """Get the standardized path for an audio file"""
        audio_dir = self.get_video_subdirectory(video_id, 'audios')
        return audio_dir / f"audio_track_{stream_index}.{codec}"
    
    def get_frame_file_path(self, video_id: int, frame_number: int, is_key_frame: bool = False) -> Path:
        """Get the standardized path for a frame file"""
        frames_dir = self.get_video_subdirectory(video_id, 'frames')
        prefix = "keyframe" if is_key_frame else "frame"
        return frames_dir / f"{prefix}_{frame_number:06d}.jpg"
    
    def get_subtitle_file_path(self, video_id: int, language: str = "zh-cn") -> Path:
        """Get the standardized path for a subtitle file"""
        subtitles_dir = self.get_video_subdirectory(video_id, 'subtitles')
        return subtitles_dir / f"subtitles_{language}.srt"
    
    def get_thumbnail_file_path(self, video_id: int, thumbnail_type: str = "main") -> Path:
        """Get the standardized path for a thumbnail file"""
        thumbnails_dir = self.get_video_subdirectory(video_id, 'thumbnails')
        return thumbnails_dir / f"thumbnail_{thumbnail_type}.jpg"
    
    def move_video_to_organized_structure(
        self, 
        video_id: int, 
        current_path: str, 
        original_filename: str
    ) -> Path:
        """Move a video file to the organized structure"""
        # Create directory structure
        self.create_video_directory_structure(video_id)
        
        # Determine new path
        new_path = self.get_original_video_path(video_id, original_filename)
        
        # Move file if it's not already in the right place
        if Path(current_path) != new_path:
            shutil.move(current_path, new_path)
            logger.info(f"Moved video {video_id} from {current_path} to {new_path}")
        
        return new_path
    
    def migrate_existing_files(
        self, 
        video_id: int, 
        current_files: Dict[str, List[str]]
    ) -> Dict[str, List[Path]]:
        """Migrate existing files to the new organized structure"""
        # Create directory structure
        dirs = self.create_video_directory_structure(video_id)
        
        migrated_files = {}
        
        # Migrate audio files
        if 'audios' in current_files:
            migrated_files['audios'] = []
            for i, audio_file in enumerate(current_files['audios']):
                if os.path.exists(audio_file):
                    # Extract codec from filename
                    codec = Path(audio_file).suffix[1:]  # Remove the dot
                    new_path = self.get_audio_file_path(video_id, i, codec)
                    shutil.move(audio_file, new_path)
                    migrated_files['audios'].append(new_path)
                    logger.info(f"Migrated audio file to {new_path}")
        
        # Migrate frame files
        if 'frames' in current_files:
            migrated_files['frames'] = []
            for i, frame_file in enumerate(current_files['frames']):
                if os.path.exists(frame_file):
                    new_path = self.get_frame_file_path(video_id, i + 1, is_key_frame=True)
                    shutil.move(frame_file, new_path)
                    migrated_files['frames'].append(new_path)
                    logger.info(f"Migrated frame file to {new_path}")
        
        # Migrate subtitle files
        if 'subtitles' in current_files:
            migrated_files['subtitles'] = []
            for subtitle_file in current_files['subtitles']:
                if os.path.exists(subtitle_file):
                    new_path = self.get_subtitle_file_path(video_id)
                    shutil.move(subtitle_file, new_path)
                    migrated_files['subtitles'].append(new_path)
                    logger.info(f"Migrated subtitle file to {new_path}")
        
        # Migrate thumbnail files
        if 'thumbnails' in current_files:
            migrated_files['thumbnails'] = []
            for thumbnail_file in current_files['thumbnails']:
                if os.path.exists(thumbnail_file):
                    new_path = self.get_thumbnail_file_path(video_id)
                    shutil.move(thumbnail_file, new_path)
                    migrated_files['thumbnails'].append(new_path)
                    logger.info(f"Migrated thumbnail file to {new_path}")
        
        return migrated_files
    
    def cleanup_old_directories(self, old_paths: List[str]):
        """Clean up old directory structures after migration"""
        for old_path in old_paths:
            if os.path.exists(old_path) and os.path.isdir(old_path):
                try:
                    # Only remove if directory is empty
                    os.rmdir(old_path)
                    logger.info(f"Cleaned up empty directory: {old_path}")
                except OSError:
                    # Directory not empty, leave it
                    logger.warning(f"Directory not empty, skipping cleanup: {old_path}")


# Global instance
file_organizer = VideoFileOrganizer()
