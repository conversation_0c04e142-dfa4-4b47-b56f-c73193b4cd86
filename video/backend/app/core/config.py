"""
应用配置管理
"""

from pydantic_settings import BaseSettings
from typing import List
import os


class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    APP_NAME: str = "短剧视频分析剪辑软件"
    DEBUG: bool = True
    VERSION: str = "1.0.0"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./app.db"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 文件存储配置
    UPLOAD_DIR: str = "../storage/uploads"  # Temporary upload directory
    PROCESSED_DIR: str = "../storage/processed"
    THUMBNAIL_DIR: str = "../storage/thumbnails"
    EXPORT_DIR: str = "../storage/exports"

    # 新的标准化存储配置
    VIDEOS_STORAGE_DIR: str = "../storage/videos"  # Base directory for organized video storage
    MAX_FILE_SIZE: int = 1024 * 1024 * 1024 * 2  # 2GB
    
    # 视频处理配置
    VIDEO_FORMATS: List[str] = [".mp4", ".mov", ".avi", ".mkv", ".wmv"]
    
    # 任务配置
    MAX_CONCURRENT_TASKS: int = 3
    TASK_TIMEOUT: int = 3600  # 1小时
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建配置实例
settings = Settings()

# 确保存储目录存在
for directory in [
    settings.UPLOAD_DIR,
    settings.PROCESSED_DIR,
    settings.THUMBNAIL_DIR,
    settings.EXPORT_DIR,
    settings.VIDEOS_STORAGE_DIR
]:
    os.makedirs(directory, exist_ok=True)
