"""
Video analysis service for comprehensive video processing
"""

import os
import json
import time
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from loguru import logger

from app.models.task import Video, AudioTrack, VideoFrame, BitrateStats
from app.services.ffmpeg_service import ffmpeg_service
from app.services.bitrate_stats_service import BitrateStatsService
from app.utils.file_organization import file_organizer


class VideoAnalysisService:
    """Service for comprehensive video analysis"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def analyze_video_metadata(self, video_id: int) -> Dict:
        """
        Analyze video metadata using ffprobe and store in database
        """
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")
        
        if not os.path.exists(video.file_path):
            raise FileNotFoundError(f"Video file not found: {video.file_path}")
        
        try:
            # Extract metadata using ffprobe
            metadata = ffmpeg_service.get_video_metadata(video.file_path)
            
            # Update video basic information
            self._update_video_basic_info(video, metadata)
            
            # Process audio tracks
            self._process_audio_tracks(video, metadata.get("audio_streams", []))

            # Analyze bitrate statistics and integrate into basic info
            try:
                logger.info(f"Analyzing bitrate statistics for video {video_id}")
                bitrate_stats = self.analyze_video_bitrate_stats(
                    video_id=video_id,
                    stream_type="video",
                    aggregation="time",
                    chunk_size=30.0
                )
                logger.info(f"Bitrate statistics analysis completed for video {video_id}")

                # Add bitrate stats to metadata
                metadata["bitrate_stats"] = {
                    "avg_fps": bitrate_stats.avg_fps,
                    "num_frames": bitrate_stats.num_frames,
                    "avg_bitrate": bitrate_stats.avg_bitrate,
                    "avg_bitrate_over_chunks": bitrate_stats.avg_bitrate_over_chunks,
                    "max_bitrate": bitrate_stats.max_bitrate,
                    "min_bitrate": bitrate_stats.min_bitrate,
                    "max_bitrate_factor": bitrate_stats.max_bitrate_factor,
                    "duration": bitrate_stats.duration,
                    "bitrate_per_chunk": bitrate_stats.bitrate_per_chunk,
                    "plot_data": bitrate_stats.plot_data
                }

            except Exception as e:
                logger.warning(f"Failed to analyze bitrate statistics for video {video_id}: {e}")
                # Continue without bitrate stats if analysis fails

            # Commit changes
            self.db.commit()

            logger.info(f"Successfully analyzed video metadata for video {video_id}")
            return metadata
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to analyze video metadata for video {video_id}: {e}")
            raise
    
    def _update_video_basic_info(self, video: Video, metadata: Dict):
        """Update video basic information from metadata"""
        format_info = metadata.get("format", {})
        video_streams = metadata.get("video_streams", [])
        
        # Update from format information
        if format_info:
            video.duration = format_info.get("duration")
            video.bitrate = format_info.get("bit_rate")
        
        # Update from first video stream
        if video_streams:
            video_stream = video_streams[0]
            video.resolution = f"{video_stream.get('width')}x{video_stream.get('height')}"
            video.fps = video_stream.get("fps")
            video.codec = video_stream.get("codec_name")
    
    def _process_audio_tracks(self, video: Video, audio_streams: List[Dict]):
        """Process and store audio track information"""
        # Remove existing audio tracks
        self.db.query(AudioTrack).filter(AudioTrack.video_id == video.id).delete()
        
        # Add new audio tracks
        for stream in audio_streams:
            audio_track = AudioTrack(
                video_id=video.id,
                stream_index=stream.get("index"),
                codec_name=stream.get("codec_name"),
                codec_long_name=stream.get("codec_long_name"),
                sample_rate=stream.get("sample_rate"),
                channels=stream.get("channels"),
                channel_layout=stream.get("channel_layout"),
                bit_rate=stream.get("bit_rate"),
                duration=stream.get("duration"),
                tags=stream.get("tags", {})
            )
            self.db.add(audio_track)
    
    def extract_audio_tracks(self, video_id: int) -> List[str]:
        """Extract all audio tracks from video using organized directory structure"""
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")

        # Create organized directory structure
        file_organizer.create_video_directory_structure(video_id)
        extracted_files = []

        # Get audio tracks from database
        audio_tracks = self.db.query(AudioTrack).filter(AudioTrack.video_id == video_id).all()

        for track in audio_tracks:
            try:
                # Get standardized audio file path
                codec = self._get_audio_extension(track.codec_name)
                output_path = file_organizer.get_audio_file_path(video_id, track.stream_index, codec)

                # Extract audio using ffmpeg
                success = ffmpeg_service.extract_audio(
                    video.file_path,
                    str(output_path),
                    track.stream_index
                )

                if success:
                    # Update database with file path
                    track.file_path = str(output_path)
                    extracted_files.append(str(output_path))
                    logger.info(f"Extracted audio track {track.stream_index} to {output_path}")
                else:
                    logger.warning(f"Failed to extract audio track {track.stream_index}")

            except Exception as e:
                logger.error(f"Error extracting audio track {track.stream_index}: {e}")

        self.db.commit()
        return extracted_files
    
    def extract_video_key_frames(self, video_id: int) -> List[str]:
        """Extract only key frames from video using organized directory structure"""
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")

        # Create organized directory structure
        file_organizer.create_video_directory_structure(video_id)

        try:
            # Extract key frames using ffmpeg
            key_frame_files = ffmpeg_service.extract_key_frames(video.file_path, video_id)

            # Remove existing frame records
            self.db.query(VideoFrame).filter(VideoFrame.video_id == video_id).delete()

            # Store key frame information in database
            for i, (frame_path, timestamp, width, height) in enumerate(key_frame_files):
                # Get frame file info
                file_size = os.path.getsize(frame_path) if os.path.exists(frame_path) else 0

                video_frame = VideoFrame(
                    video_id=video_id,
                    frame_number=i + 1,
                    timestamp=timestamp,
                    file_path=frame_path,
                    width=width,
                    height=height,
                    file_size=file_size,
                    is_key_frame=True
                )
                self.db.add(video_frame)

            self.db.commit()
            logger.info(f"Extracted {len(key_frame_files)} key frames for video {video_id}")

            # Set the first key frame as thumbnail if no thumbnail is set
            if key_frame_files and not video.key_frame_thumbnail_id:
                first_frame = self.db.query(VideoFrame).filter(
                    VideoFrame.video_id == video_id
                ).order_by(VideoFrame.timestamp).first()
                if first_frame:
                    video.key_frame_thumbnail_id = first_frame.id
                    self.db.commit()

            return [frame_path for frame_path, _, _, _ in key_frame_files]
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to extract frames for video {video_id}: {e}")
            raise
    
    def _get_audio_extension(self, codec_name: str) -> str:
        """Get appropriate file extension for audio codec"""
        codec_extensions = {
            "aac": "aac",
            "mp3": "mp3",
            "ac3": "ac3",
            "flac": "flac",
            "opus": "opus",
            "vorbis": "ogg",
            "pcm_s16le": "wav",
            "pcm_s24le": "wav",
            "pcm_s32le": "wav"
        }
        return codec_extensions.get(codec_name, "audio")
    
    def get_video_analysis_summary(self, video_id: int) -> Dict:
        """Get comprehensive analysis summary for a video"""
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")

        # Get related data
        audio_tracks = self.db.query(AudioTrack).filter(AudioTrack.video_id == video_id).all()
        frames = self.db.query(VideoFrame).filter(VideoFrame.video_id == video_id).all()

        return {
            "video_info": {
                "id": video.id,
                "filename": video.filename,
                "duration": video.duration,
                "resolution": video.resolution,
                "fps": video.fps,
                "codec": video.codec,
                "bitrate": video.bitrate,
                "file_size": video.file_size
            },
            "audio_tracks": [
                {
                    "id": track.id,
                    "stream_index": track.stream_index,
                    "codec": track.codec_name,
                    "sample_rate": track.sample_rate,
                    "channels": track.channels,
                    "duration": track.duration,
                    "file_path": track.file_path
                }
                for track in audio_tracks
            ],
            "frames": {
                "total_count": len(frames),
                "extraction_fps": frames[0].extraction_fps if frames else None,
                "frame_files": [frame.file_path for frame in frames]
            }
        }

    def generate_comprehensive_video_info(self, video_id: int) -> str:
        """
        Generate comprehensive video information and save as JSON file
        Returns the path to the generated JSON file
        """
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")

        if not os.path.exists(video.file_path):
            raise FileNotFoundError(f"Video file not found: {video.file_path}")

        try:
            # Get comprehensive video information
            logger.info(f"Starting comprehensive analysis for video {video_id}")
            comprehensive_data = ffmpeg_service.get_comprehensive_video_info(video.file_path)

            # Generate output filename based on video filename
            video_filename = os.path.splitext(video.filename)[0]
            json_filename = f"{video_filename}.json"

            # Save to storage/uploads directory (same as video file)
            storage_dir = os.path.dirname(video.file_path)
            json_file_path = os.path.join(storage_dir, json_filename)

            # Write comprehensive data to JSON file
            with open(json_file_path, 'w', encoding='utf-8') as f:
                json.dump(comprehensive_data, f, indent=4, ensure_ascii=False)

            logger.info(f"Comprehensive video info saved to {json_file_path}")

            # Log file size for reference
            file_size = os.path.getsize(json_file_path)
            logger.info(f"Generated JSON file size: {file_size / (1024*1024):.2f} MB")

            return json_file_path

        except Exception as e:
            logger.error(f"Failed to generate comprehensive video info for video {video_id}: {e}")
            raise

    def analyze_video_bitrate_stats(
        self,
        video_id: int,
        stream_type: str = "video",
        aggregation: str = "time",
        chunk_size: float = 30.0,
        plot_width: int = 70,
        plot_height: int = 18
    ) -> BitrateStats:
        """
        分析视频比特率统计并存储到数据库

        Args:
            video_id: 视频ID
            stream_type: 流类型 ("video" 或 "audio")
            aggregation: 聚合方式 ("time" 或 "gop")
            chunk_size: 块大小（秒，仅当aggregation="time"时有效）
            plot_width: 图表宽度
            plot_height: 图表高度

        Returns:
            BitrateStats: 比特率统计对象
        """
        try:
            logger.info(f"Starting bitrate statistics analysis for video {video_id}")

            # 创建比特率统计服务
            bitrate_service = BitrateStatsService(self.db)

            # 执行比特率分析
            bitrate_stats = bitrate_service.analyze_video_bitrate(
                video_id=video_id,
                stream_type=stream_type,
                aggregation=aggregation,
                chunk_size=chunk_size,
                plot_width=plot_width,
                plot_height=plot_height
            )

            logger.info(f"Bitrate statistics analysis completed for video {video_id}")
            return bitrate_stats

        except Exception as e:
            logger.error(f"Failed to analyze bitrate statistics for video {video_id}: {e}")
            raise

    def get_bitrate_stats_summary(self, video_id: int) -> Optional[Dict]:
        """获取视频比特率统计摘要"""
        try:
            bitrate_service = BitrateStatsService(self.db)
            return bitrate_service.get_bitrate_stats_summary(video_id)
        except Exception as e:
            logger.error(f"Failed to get bitrate stats summary for video {video_id}: {e}")
            return None
