"""
分析结果API端点
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.models.task import AnalysisResult

router = APIRouter()


@router.get("/{video_id}", response_model=List[dict])
async def get_analysis_results(video_id: int, db: Session = Depends(get_db)):
    """获取视频分析结果"""
    results = db.query(AnalysisResult).filter(AnalysisResult.video_id == video_id).all()
    return [
        {
            "id": result.id,
            "video_id": result.video_id,
            "step": result.step,
            "result": result.result,
            "confidence": result.confidence,
            "processing_time": result.processing_time,  # Keep for backward compatibility
            "processing_duration_seconds": result.processing_duration_seconds,
            "started_at": result.started_at.isoformat() if result.started_at else None,
            "completed_at": result.completed_at.isoformat() if result.completed_at else None,
            "created_at": result.created_at.isoformat()
        }
        for result in results
    ]


@router.get("/{video_id}/step/{step}")
async def get_analysis_step(video_id: int, step: str, db: Session = Depends(get_db)):
    """获取特定步骤的分析结果"""
    result = db.query(AnalysisResult).filter(
        AnalysisResult.video_id == video_id,
        AnalysisResult.step == step
    ).first()

    if not result:
        raise HTTPException(status_code=404, detail="Analysis result not found")

    return {
        "id": result.id,
        "video_id": result.video_id,
        "step": result.step,
        "result": result.result,
        "confidence": result.confidence,
        "processing_time": result.processing_time,  # Keep for backward compatibility
        "processing_duration_seconds": result.processing_duration_seconds,
        "started_at": result.started_at.isoformat() if result.started_at else None,
        "completed_at": result.completed_at.isoformat() if result.completed_at else None,
        "created_at": result.created_at.isoformat()
    }


@router.get("/{video_id}/results")
async def get_all_analysis_results(video_id: int, db: Session = Depends(get_db)):
    """获取视频的所有分析结果"""
    results = db.query(AnalysisResult).filter(
        AnalysisResult.video_id == video_id
    ).all()

    return [
        {
            "id": result.id,
            "video_id": result.video_id,
            "step": result.step,
            "result": result.result,
            "confidence": result.confidence,
            "processing_time": result.processing_time,  # Keep for backward compatibility
            "processing_duration_seconds": result.processing_duration_seconds,
            "started_at": result.started_at.isoformat() if result.started_at else None,
            "completed_at": result.completed_at.isoformat() if result.completed_at else None,
            "created_at": result.created_at.isoformat()
        }
        for result in results
    ]
