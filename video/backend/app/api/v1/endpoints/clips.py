"""
片段管理API端点
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from app.core.database import get_db
from app.models.task import Clip

router = APIRouter()


@router.get("/", response_model=List[dict])
async def get_clips(
    video_id: Optional[int] = None,
    clip_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取片段列表"""
    query = db.query(Clip)

    if video_id:
        query = query.filter(Clip.video_id == video_id)
    if clip_type:
        query = query.filter(Clip.clip_type == clip_type)

    clips = query.order_by(Clip.created_at.desc()).all()

    return [
        {
            "id": clip.id,
            "video_id": clip.video_id,
            "start_time": clip.start_time,
            "end_time": clip.end_time,
            "duration": clip.duration,
            "clip_type": clip.clip_type,
            "title": clip.title,
            "description": clip.description,
            "tags": clip.tags,
            "metadata": clip.clip_metadata,
            "quality_score": clip.quality_score,
            "importance_score": clip.importance_score,
            "thumbnail_path": clip.thumbnail_path,
            "created_at": clip.created_at.isoformat()
        }
        for clip in clips
    ]


@router.get("/{clip_id}", response_model=dict)
async def get_clip(clip_id: int, db: Session = Depends(get_db)):
    """获取片段详情"""
    clip = db.query(Clip).filter(Clip.id == clip_id).first()
    if not clip:
        raise HTTPException(status_code=404, detail="Clip not found")

    return {
        "id": clip.id,
        "video_id": clip.video_id,
        "start_time": clip.start_time,
        "end_time": clip.end_time,
        "duration": clip.duration,
        "clip_type": clip.clip_type,
        "title": clip.title,
        "description": clip.description,
        "tags": clip.tags,
        "metadata": clip.clip_metadata,
        "quality_score": clip.quality_score,
        "importance_score": clip.importance_score,
        "file_path": clip.file_path,
        "thumbnail_path": clip.thumbnail_path,
        "created_at": clip.created_at.isoformat()
    }


@router.get("/{video_id}/generate")
async def generate_clips(video_id: int, db: Session = Depends(get_db)):
    """生成视频片段"""
    # 这里应该调用片段生成服务
    # 暂时返回模拟响应  
    return {
        "message": "Clip generation started",
        "video_id": video_id,
        "status": "processing"
    }
