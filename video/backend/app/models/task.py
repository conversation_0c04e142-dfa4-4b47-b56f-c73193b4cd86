"""
任务相关数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, Float, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base


class Task(Base):
    """任务模型"""
    __tablename__ = "tasks"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    status = Column(String(50), default="pending", index=True)  # pending, processing, completed, failed, paused
    config = Column(JSON)  # 任务配置参数
    progress = Column(Float, default=0.0)  # 进度百分比
    error_message = Column(Text)  # 错误信息
    celery_task_id = Column(String(255), nullable=True, index=True)  # Celery任务ID，用于暂停/取消任务
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关联关系
    videos = relationship("Video", back_populates="task", cascade="all, delete-orphan")


class Video(Base):
    """视频模型"""
    __tablename__ = "videos"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("tasks.id"), nullable=False)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)  # 文件大小(字节)
    duration = Column(Float)  # 时长(秒)
    resolution = Column(String(20))  # 分辨率 "1920x1080"
    fps = Column(Float)  # 帧率
    codec = Column(String(50))  # 编码格式
    bitrate = Column(Integer)  # 比特率
    key_frame_thumbnail_id = Column(Integer, ForeignKey("video_frames.id"), nullable=True)  # 关键帧缩略图ID
    status = Column(String(50), default="uploaded")  # uploaded, analyzing, analyzed, failed
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关联关系
    task = relationship("Task", back_populates="videos")
    analysis_results = relationship("AnalysisResult", back_populates="video", cascade="all, delete-orphan")
    clips = relationship("Clip", back_populates="video", cascade="all, delete-orphan")
    audio_tracks = relationship("AudioTrack", back_populates="video", cascade="all, delete-orphan")
    subtitles = relationship("Subtitle", back_populates="video", cascade="all, delete-orphan")
    frames = relationship("VideoFrame", back_populates="video", foreign_keys="VideoFrame.video_id", cascade="all, delete-orphan")
    key_frame_thumbnail = relationship("VideoFrame", foreign_keys=[key_frame_thumbnail_id])
    bitrate_stats = relationship("BitrateStats", back_populates="video", uselist=False, cascade="all, delete-orphan")
    scene_changes = relationship("SceneChange", back_populates="video", cascade="all, delete-orphan")


class AnalysisResult(Base):
    """分析结果模型"""
    __tablename__ = "analysis_results"
    
    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False)
    step = Column(String(50), nullable=False, index=True)  # basic_analysis, content_analysis, plot_analysis
    result = Column(JSON, nullable=False)  # 分析结果数据
    confidence = Column(Float)  # 置信度
    processing_time = Column(Float)  # 处理时间(秒) - 保留用于向后兼容
    processing_duration_seconds = Column(Float)  # 处理持续时间(秒)
    started_at = Column(DateTime(timezone=True))  # 开始时间
    completed_at = Column(DateTime(timezone=True))  # 完成时间
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关联关系
    video = relationship("Video", back_populates="analysis_results")


class Clip(Base):
    """视频片段模型"""
    __tablename__ = "clips"
    
    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False)
    start_time = Column(Float, nullable=False)  # 开始时间(秒)
    end_time = Column(Float, nullable=False)  # 结束时间(秒)
    duration = Column(Float, nullable=False)  # 片段时长(秒)
    clip_type = Column(String(50), index=True)  # character, scene, plot, time_based
    title = Column(String(255))  # 片段标题
    description = Column(Text)  # 片段描述
    tags = Column(JSON)  # 标签列表
    clip_metadata = Column(JSON)  # 元数据(人物、场景、情感等)
    quality_score = Column(Float)  # 质量评分
    importance_score = Column(Float)  # 重要性评分
    file_path = Column(String(500))  # 片段文件路径(如果已导出)
    thumbnail_path = Column(String(500))  # 缩略图路径
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关联关系
    video = relationship("Video", back_populates="clips")


class Project(Base):
    """剪辑项目模型"""
    __tablename__ = "projects"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    timeline_data = Column(JSON)  # 时间轴数据
    export_settings = Column(JSON)  # 导出设置
    status = Column(String(50), default="draft")  # draft, exporting, completed
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关联关系
    project_clips = relationship("ProjectClip", back_populates="project", cascade="all, delete-orphan")


class ProjectClip(Base):
    """项目片段关联模型"""
    __tablename__ = "project_clips"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    clip_id = Column(Integer, ForeignKey("clips.id"), nullable=False)
    track_index = Column(Integer, default=0)  # 轨道索引
    start_position = Column(Float, nullable=False)  # 在时间轴上的开始位置
    trim_start = Column(Float, default=0.0)  # 片段裁剪开始时间
    trim_end = Column(Float)  # 片段裁剪结束时间
    volume = Column(Float, default=1.0)  # 音量
    effects = Column(JSON)  # 特效配置
    transitions = Column(JSON)  # 转场配置
    order_index = Column(Integer, default=0)  # 排序索引

    # 关联关系
    project = relationship("Project", back_populates="project_clips")
    clip = relationship("Clip")


class AudioTrack(Base):
    """音频轨道模型"""
    __tablename__ = "audio_tracks"

    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False)
    stream_index = Column(Integer, nullable=False)  # 在视频中的流索引
    codec_name = Column(String(50))  # 编码格式
    codec_long_name = Column(String(255))  # 编码格式详细名称
    sample_rate = Column(Integer)  # 采样率
    channels = Column(Integer)  # 声道数
    channel_layout = Column(String(50))  # 声道布局
    bit_rate = Column(Integer)  # 比特率
    duration = Column(Float)  # 时长(秒)
    file_path = Column(String(500))  # 提取的音频文件路径
    tags = Column(JSON)  # 标签信息
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关联关系
    video = relationship("Video", back_populates="audio_tracks")
    subtitles = relationship("Subtitle", back_populates="audio_track", cascade="all, delete-orphan")


class Subtitle(Base):
    """字幕模型"""
    __tablename__ = "subtitles"

    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False)
    audio_track_id = Column(Integer, ForeignKey("audio_tracks.id"), nullable=True)  # 关联的音频轨道
    subtitle_type = Column(String(20), nullable=False, index=True)  # manual, auto_generated
    language = Column(String(10), default="zh-cn")  # 语言代码
    file_path = Column(String(500))  # SRT文件路径
    content = Column(Text)  # 字幕内容(JSON格式存储时间轴信息)
    confidence = Column(Float)  # 自动生成字幕的置信度
    processing_time = Column(Float)  # 处理时间(秒)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关联关系
    video = relationship("Video", back_populates="subtitles")
    audio_track = relationship("AudioTrack", back_populates="subtitles")


class VideoFrame(Base):
    """视频帧模型"""
    __tablename__ = "video_frames"

    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False)
    frame_number = Column(Integer, nullable=False)  # 帧序号
    timestamp = Column(Float, nullable=False)  # 时间戳(秒)
    file_path = Column(String(500), nullable=False)  # 帧图片文件路径
    width = Column(Integer)  # 图片宽度
    height = Column(Integer)  # 图片高度
    file_size = Column(Integer)  # 文件大小(字节)
    extraction_fps = Column(Float)  # 提取时的FPS设置
    is_key_frame = Column(Boolean, default=True)  # 是否为关键帧
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关联关系
    video = relationship("Video", back_populates="frames", foreign_keys=[video_id])


class BitrateStats(Base):
    """比特率统计模型"""
    __tablename__ = "bitrate_stats"

    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False, unique=True)

    # 基本统计信息
    stream_type = Column(String(20), nullable=False)  # video 或 audio
    avg_fps = Column(Float)  # 平均帧率
    num_frames = Column(Integer)  # 总帧数
    avg_bitrate = Column(Float)  # 平均比特率 (kbps)
    avg_bitrate_over_chunks = Column(Float)  # 基于块计算的平均比特率 (kbps)
    max_bitrate = Column(Float)  # 最大比特率 (kbps)
    min_bitrate = Column(Float)  # 最小比特率 (kbps)
    max_bitrate_factor = Column(Float)  # 峰值与平均值的比率

    # 聚合设置
    aggregation = Column(String(20), nullable=False)  # time 或 gop
    chunk_size = Column(Float)  # 块大小（秒，仅当aggregation=time时有效）
    duration = Column(Float)  # 总时长（秒）

    # 时间序列数据（JSON格式存储）
    bitrate_per_chunk = Column(JSON)  # 每个块的比特率数组

    # 图表数据
    plot_data = Column(Text)  # ASCII图表数据
    plot_width = Column(Integer, default=70)  # 图表宽度
    plot_height = Column(Integer, default=18)  # 图表高度

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    video = relationship("Video", back_populates="bitrate_stats")


class SceneChange(Base):
    """场景切换模型"""
    __tablename__ = "scene_changes"

    id = Column(Integer, primary_key=True, index=True)
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False)

    # 场景信息
    scene_number = Column(Integer, nullable=False)  # 场景编号
    start_time = Column(Float, nullable=False)  # 开始时间（秒）
    end_time = Column(Float, nullable=False)  # 结束时间（秒）
    duration = Column(Float, nullable=False)  # 场景时长（秒）
    start_frame = Column(Integer)  # 开始帧号
    end_frame = Column(Integer)  # 结束帧号

    # 检测信息
    confidence = Column(Float, default=0.0)  # 检测置信度
    detector_type = Column(String(50))  # 检测器类型 (content, threshold)
    threshold = Column(Float)  # 检测阈值

    # 元数据
    scene_metadata = Column(JSON)  # 额外的元数据信息

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    video = relationship("Video", back_populates="scene_changes")
