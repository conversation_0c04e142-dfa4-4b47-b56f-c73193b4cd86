"""Schema improvements for video processing system

Revision ID: 0002
Revises: 0001
Create Date: 2025-01-04 12:30:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '0002'
down_revision = '0001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 1. Update videos table - replace thumbnail_path with key_frame_thumbnail_id
    op.add_column('videos', sa.Column('key_frame_thumbnail_id', sa.Integer(), nullable=True))
    op.create_foreign_key('fk_videos_key_frame_thumbnail', 'videos', 'video_frames', ['key_frame_thumbnail_id'], ['id'])
    
    # Remove thumbnail_path column (will be done after data migration)
    # op.drop_column('videos', 'thumbnail_path')
    
    # 2. Update video_frames table - add is_key_frame field
    op.add_column('video_frames', sa.Column('is_key_frame', sa.<PERSON>(), nullable=True, default=True))
    
    # 3. Update analysis_results table - improve processing time tracking
    op.add_column('analysis_results', sa.Column('processing_duration_seconds', sa.Float(), nullable=True))
    op.add_column('analysis_results', sa.Column('started_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('analysis_results', sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True))
    
    # Keep processing_time for backward compatibility during migration
    # op.drop_column('analysis_results', 'processing_time')
    
    # 4. Add constraints for task status workflow
    # Create check constraint for valid task statuses
    op.create_check_constraint(
        'ck_tasks_status_valid',
        'tasks',
        "status IN ('pending', 'processing', 'completed', 'failed', 'paused', 'cancelled')"
    )
    
    # 5. Add constraints for analysis_results step values
    op.create_check_constraint(
        'ck_analysis_results_step_valid',
        'analysis_results',
        "step IN ('basic_analysis', 'content_analysis', 'plot_analysis')"
    )
    
    # 6. Add index for better query performance
    op.create_index('ix_video_frames_is_key_frame', 'video_frames', ['is_key_frame'])
    op.create_index('ix_video_frames_timestamp', 'video_frames', ['timestamp'])
    op.create_index('ix_analysis_results_started_at', 'analysis_results', ['started_at'])
    op.create_index('ix_analysis_results_completed_at', 'analysis_results', ['completed_at'])


def downgrade() -> None:
    # Remove indexes
    op.drop_index('ix_analysis_results_completed_at', table_name='analysis_results')
    op.drop_index('ix_analysis_results_started_at', table_name='analysis_results')
    op.drop_index('ix_video_frames_timestamp', table_name='video_frames')
    op.drop_index('ix_video_frames_is_key_frame', table_name='video_frames')
    
    # Remove check constraints
    op.drop_constraint('ck_analysis_results_step_valid', 'analysis_results', type_='check')
    op.drop_constraint('ck_tasks_status_valid', 'tasks', type_='check')
    
    # Remove new columns from analysis_results
    op.drop_column('analysis_results', 'completed_at')
    op.drop_column('analysis_results', 'started_at')
    op.drop_column('analysis_results', 'processing_duration_seconds')
    
    # Remove is_key_frame column from video_frames
    op.drop_column('video_frames', 'is_key_frame')
    
    # Remove foreign key and column from videos
    op.drop_constraint('fk_videos_key_frame_thumbnail', 'videos', type_='foreignkey')
    op.drop_column('videos', 'key_frame_thumbnail_id')
    
    # Restore thumbnail_path column
    op.add_column('videos', sa.Column('thumbnail_path', sa.String(length=500), nullable=True))
