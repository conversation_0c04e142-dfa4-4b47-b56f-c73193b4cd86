/**
 * Backward compatibility utilities for schema improvements
 * Ensures smooth transition from old to new API formats
 */

/**
 * Normalize video object to handle both old and new formats
 * @param {Object} video - Video object from API
 * @returns {Object} - Normalized video object
 */
export const normalizeVideo = (video) => {
  if (!video) return null

  return {
    ...video,
    // Ensure thumbnail compatibility
    thumbnail: video.thumbnail || getVideoThumbnailCompat(video),
    // Ensure file path compatibility
    file_url: video.file_url || getVideoUrlCompat(video),
    // Normalize processing status
    status: normalizeStatus(video.status),
    // Ensure duration is a number
    duration: parseFloat(video.duration) || 0
  }
}

/**
 * Normalize analysis result to handle both old and new formats
 * @param {Object} result - Analysis result from API
 * @returns {Object} - Normalized analysis result
 */
export const normalizeAnalysisResult = (result) => {
  if (!result) return null

  return {
    ...result,
    // Processing time compatibility
    processing_duration: result.processing_duration_seconds || result.processing_time || 0,
    processing_time: result.processing_time || result.processing_duration_seconds || 0,
    // Timestamp compatibility
    started_at: result.started_at,
    completed_at: result.completed_at,
    // Ensure step is standardized
    step: normalizeAnalysisStep(result.step),
    // Ensure confidence is a number between 0 and 1
    confidence: normalizeConfidence(result.confidence)
  }
}

/**
 * Normalize task object to handle both old and new formats
 * @param {Object} task - Task object from API
 * @returns {Object} - Normalized task object
 */
export const normalizeTask = (task) => {
  if (!task) return null

  return {
    ...task,
    // Normalize status
    status: normalizeStatus(task.status),
    // Ensure progress is a number between 0 and 100
    progress: Math.min(100, Math.max(0, parseFloat(task.progress) || 0)),
    // Normalize videos array
    videos: Array.isArray(task.videos) ? task.videos.map(normalizeVideo) : []
  }
}

/**
 * Get video thumbnail URL with backward compatibility
 * @param {Object} video - Video object
 * @returns {string} - Thumbnail URL
 */
const getVideoThumbnailCompat = (video) => {
  if (!video) return ''

  // New key frame thumbnail system
  if (video.key_frame_thumbnail_id) {
    return `http://localhost:8000/static/videos/${video.id}/frames/keyframe_${String(video.key_frame_thumbnail_id).padStart(6, '0')}.jpg`
  }

  // Old thumbnail_path system
  if (video.thumbnail_path) {
    // Check if it's already using new structure
    if (video.thumbnail_path.includes('/static/videos/')) {
      return video.thumbnail_path
    }
    // Convert old path to new structure
    return `http://localhost:8000/static/videos/${video.id}/thumbnails/thumbnail_main.jpg`
  }

  return ''
}

/**
 * Get video URL with backward compatibility
 * @param {Object} video - Video object
 * @returns {string} - Video URL
 */
const getVideoUrlCompat = (video) => {
  if (!video || !video.filename) return ''

  // Check if file_path is already using new structure
  if (video.file_path && video.file_path.includes('/static/videos/')) {
    return video.file_path
  }

  // Use new organized structure
  return `http://localhost:8000/static/videos/${video.id}/${video.filename}`
}

/**
 * Normalize status values
 * @param {string} status - Status value
 * @returns {string} - Normalized status
 */
const normalizeStatus = (status) => {
  if (!status) return 'pending'

  const statusMap = {
    // Task statuses
    'pending': 'pending',
    'processing': 'processing', 
    'completed': 'completed',
    'failed': 'failed',
    'paused': 'paused',
    'cancelled': 'cancelled',
    // Video analysis statuses
    'analyzing': 'processing',
    'analyzed': 'completed',
    'analysis_failed': 'failed'
  }

  return statusMap[status] || status
}

/**
 * Normalize analysis step names
 * @param {string} step - Step name
 * @returns {string} - Normalized step name
 */
const normalizeAnalysisStep = (step) => {
  if (!step) return step

  const stepMap = {
    'basic_info': 'basic_analysis',
    'basic_analysis': 'basic_analysis',
    'content_analysis': 'content_analysis',
    'plot_analysis': 'plot_analysis'
  }

  return stepMap[step] || step
}

/**
 * Normalize confidence value
 * @param {number} confidence - Confidence value
 * @returns {number} - Normalized confidence (0-1)
 */
const normalizeConfidence = (confidence) => {
  if (confidence === null || confidence === undefined) return null
  
  const conf = parseFloat(confidence)
  if (isNaN(conf)) return null
  
  // If confidence is > 1, assume it's a percentage and convert to decimal
  if (conf > 1) {
    return Math.min(1, conf / 100)
  }
  
  return Math.min(1, Math.max(0, conf))
}

/**
 * Handle API response with backward compatibility
 * @param {Object} response - API response
 * @param {string} type - Response type ('video', 'task', 'analysis', etc.)
 * @returns {Object} - Normalized response
 */
export const normalizeApiResponse = (response, type) => {
  if (!response) return response

  switch (type) {
    case 'video':
      return Array.isArray(response) 
        ? response.map(normalizeVideo)
        : normalizeVideo(response)
    
    case 'task':
      return Array.isArray(response)
        ? response.map(normalizeTask)
        : normalizeTask(response)
    
    case 'analysis':
      return Array.isArray(response)
        ? response.map(normalizeAnalysisResult)
        : normalizeAnalysisResult(response)
    
    default:
      return response
  }
}

/**
 * Check if the system is using new schema features
 * @param {Object} data - Data object to check
 * @returns {boolean} - True if using new schema
 */
export const isUsingNewSchema = (data) => {
  if (!data) return false

  // Check for new schema indicators
  const newSchemaIndicators = [
    'processing_duration_seconds',
    'started_at',
    'completed_at',
    'key_frame_thumbnail_id',
    'is_key_frame'
  ]

  return newSchemaIndicators.some(indicator => 
    data.hasOwnProperty(indicator) && data[indicator] !== null
  )
}

/**
 * Migrate old data format to new format
 * @param {Object} oldData - Old format data
 * @param {string} type - Data type
 * @returns {Object} - Migrated data
 */
export const migrateDataFormat = (oldData, type) => {
  if (!oldData) return oldData

  switch (type) {
    case 'analysis_result':
      return {
        ...oldData,
        processing_duration_seconds: oldData.processing_time,
        started_at: oldData.created_at ? new Date(new Date(oldData.created_at).getTime() - (oldData.processing_time * 1000)).toISOString() : null,
        completed_at: oldData.created_at
      }
    
    case 'video':
      return {
        ...oldData,
        key_frame_thumbnail_id: null // Will be set when key frames are extracted
      }
    
    default:
      return oldData
  }
}

/**
 * Format error messages for user display
 * @param {Error} error - Error object
 * @param {string} context - Context of the error
 * @returns {string} - User-friendly error message
 */
export const formatErrorMessage = (error, context = '') => {
  if (!error) return '未知错误'

  // Handle API errors
  if (error.response) {
    const status = error.response.status
    const detail = error.response.data?.detail || error.message

    switch (status) {
      case 400:
        return `请求参数错误: ${detail}`
      case 401:
        return '未授权访问，请重新登录'
      case 403:
        return '权限不足，无法执行此操作'
      case 404:
        return context ? `${context}不存在` : '资源不存在'
      case 422:
        return `数据验证失败: ${detail}`
      case 500:
        return `服务器内部错误: ${detail}`
      default:
        return `请求失败 (${status}): ${detail}`
    }
  }

  // Handle network errors
  if (error.request) {
    return '网络连接失败，请检查网络设置'
  }

  // Handle other errors
  return error.message || '操作失败，请稍后重试'
}
