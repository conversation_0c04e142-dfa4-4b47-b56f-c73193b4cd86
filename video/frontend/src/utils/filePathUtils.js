/**
 * File path utilities for handling the new organized file structure
 */

const BASE_URL = 'http://localhost:8000'

/**
 * Get video file URL using new organized structure
 * @param {Object} video - Video object with id and filename
 * @returns {string} - Full URL to video file
 */
export const getVideoUrl = (video) => {
  if (!video || !video.filename) {
    return ''
  }
  return `${BASE_URL}/static/videos/${video.id}/${video.filename}`
}

/**
 * Get video thumbnail URL using key frame system
 * @param {Object} video - Video object
 * @returns {string} - Full URL to thumbnail
 */
export const getVideoThumbnail = (video) => {
  if (!video) return ''
  
  // Handle new key frame thumbnail system
  if (video.key_frame_thumbnail_id) {
    // If we have key frame thumbnail ID, construct path to key frame
    return `${BASE_URL}/static/videos/${video.id}/frames/keyframe_${String(video.key_frame_thumbnail_id).padStart(6, '0')}.jpg`
  }
  
  // Fallback to organized thumbnail structure
  if (video.thumbnail_path) {
    return `${BASE_URL}/static/videos/${video.id}/thumbnails/thumbnail_main.jpg`
  }
  
  return ''
}

/**
 * Get frame file URL using organized structure
 * @param {number} videoId - Video ID
 * @param {number} frameNumber - Frame number
 * @param {boolean} isKeyFrame - Whether this is a key frame
 * @returns {string} - Full URL to frame file
 */
export const getFrameUrl = (videoId, frameNumber, isKeyFrame = true) => {
  const prefix = isKeyFrame ? 'keyframe' : 'frame'
  const paddedNumber = String(frameNumber).padStart(6, '0')
  return `${BASE_URL}/static/videos/${videoId}/frames/${prefix}_${paddedNumber}.jpg`
}

/**
 * Get audio file URL using organized structure
 * @param {number} videoId - Video ID
 * @param {number} trackIndex - Audio track index
 * @param {string} codec - Audio codec (default: wav)
 * @returns {string} - Full URL to audio file
 */
export const getAudioUrl = (videoId, trackIndex, codec = 'wav') => {
  return `${BASE_URL}/static/videos/${videoId}/audios/audio_track_${trackIndex}.${codec}`
}

/**
 * Get subtitle file URL using organized structure
 * @param {number} videoId - Video ID
 * @param {string} language - Language code (default: zh-cn)
 * @returns {string} - Full URL to subtitle file
 */
export const getSubtitleUrl = (videoId, language = 'zh-cn') => {
  return `${BASE_URL}/static/videos/${videoId}/subtitles/subtitles_${language}.srt`
}

/**
 * Get video directory base URL
 * @param {number} videoId - Video ID
 * @returns {string} - Base URL for video directory
 */
export const getVideoDirectoryUrl = (videoId) => {
  return `${BASE_URL}/static/videos/${videoId}`
}

/**
 * Legacy file path converter for backward compatibility
 * Converts old file paths to new organized structure
 * @param {string} oldPath - Old file path
 * @param {number} videoId - Video ID
 * @returns {string} - New organized file path
 */
export const convertLegacyPath = (oldPath, videoId) => {
  if (!oldPath || !videoId) return oldPath
  
  // Convert old upload paths
  if (oldPath.includes('/static/uploads/')) {
    const filename = oldPath.split('/').pop()
    return `${BASE_URL}/static/videos/${videoId}/${filename}`
  }
  
  // Convert old thumbnail paths
  if (oldPath.includes('/static/thumbnails/')) {
    return `${BASE_URL}/static/videos/${videoId}/thumbnails/thumbnail_main.jpg`
  }
  
  // Convert old frame paths
  if (oldPath.includes('/frames/')) {
    const filename = oldPath.split('/').pop()
    return `${BASE_URL}/static/videos/${videoId}/frames/${filename}`
  }
  
  // Convert old audio paths
  if (oldPath.includes('/audio/')) {
    const filename = oldPath.split('/').pop()
    return `${BASE_URL}/static/videos/${videoId}/audios/${filename}`
  }
  
  return oldPath
}

/**
 * Check if a file path uses the new organized structure
 * @param {string} filePath - File path to check
 * @returns {boolean} - True if using new structure
 */
export const isOrganizedPath = (filePath) => {
  return filePath && filePath.includes('/static/videos/')
}

/**
 * Extract video ID from organized file path
 * @param {string} filePath - Organized file path
 * @returns {number|null} - Video ID or null if not found
 */
export const extractVideoIdFromPath = (filePath) => {
  if (!isOrganizedPath(filePath)) return null
  
  const match = filePath.match(/\/static\/videos\/(\d+)\//)
  return match ? parseInt(match[1], 10) : null
}

/**
 * Format processing duration for display
 * @param {number} seconds - Duration in seconds
 * @returns {string} - Formatted duration string
 */
export const formatProcessingDuration = (seconds) => {
  if (!seconds || seconds < 0) return '0s'
  
  if (seconds < 60) {
    return `${seconds.toFixed(1)}s`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}m ${remainingSeconds}s`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}h ${minutes}m`
  }
}

/**
 * Format timestamp for display
 * @param {string} timestamp - ISO timestamp string
 * @returns {string} - Formatted timestamp
 */
export const formatTimestamp = (timestamp) => {
  if (!timestamp) return ''
  
  try {
    const date = new Date(timestamp)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    console.error('Error formatting timestamp:', error)
    return timestamp
  }
}
