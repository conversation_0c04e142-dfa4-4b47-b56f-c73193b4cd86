import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const isLoading = ref(false)
  const notification = ref({
    show: false,
    type: 'info', // success, error, warning, info
    title: '',
    message: ''
  })

  // 操作
  const setLoading = (loading) => {
    isLoading.value = loading
  }

  const showNotification = ({ type = 'info', title, message, duration = 5000 }) => {
    notification.value = {
      show: true,
      type,
      title,
      message
    }

    // 自动隐藏通知
    if (duration > 0) {
      setTimeout(() => {
        hideNotification()
      }, duration)
    }
  }

  const hideNotification = () => {
    notification.value.show = false
  }

  const showSuccess = (title, message) => {
    showNotification({ type: 'success', title, message })
  }

  const showError = (title, message) => {
    showNotification({ type: 'error', title, message })
  }

  const showWarning = (title, message) => {
    showNotification({ type: 'warning', title, message })
  }

  const showInfo = (title, message) => {
    showNotification({ type: 'info', title, message })
  }

  return {
    // 状态
    isLoading,
    notification,
    
    // 操作
    setLoading,
    showNotification,
    hideNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo
  }
})
