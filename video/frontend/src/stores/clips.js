import { defineStore } from 'pinia'
import { ref } from 'vue'
import client from '@/api/client'

export const useClipStore = defineStore('clips', () => {
  // 状态
  const clips = ref([])
  const currentClip = ref(null)

  // 操作
  const fetchClips = async (filters = {}) => {
    try {
      const params = new URLSearchParams()
      
      if (filters.video_id) {
        params.append('video_id', filters.video_id)
      }
      if (filters.clip_type) {
        params.append('clip_type', filters.clip_type)
      }
      if (filters.min_duration) {
        params.append('min_duration', filters.min_duration)
      }
      if (filters.max_duration) {
        params.append('max_duration', filters.max_duration)
      }
      if (filters.min_quality) {
        params.append('min_quality', filters.min_quality)
      }
      if (filters.min_importance) {
        params.append('min_importance', filters.min_importance)
      }
      
      const url = params.toString() ? `/clips?${params.toString()}` : '/clips'
      const response = await client.get(url)
      clips.value = response
      return response
    } catch (error) {
      console.error('Failed to fetch clips:', error)
      throw error
    }
  }

  const fetchClip = async (clipId) => {
    try {
      const response = await client.get(`/clips/${clipId}`)
      currentClip.value = response
      return response
    } catch (error) {
      console.error('Failed to fetch clip:', error)
      throw error
    }
  }

  const generateClips = async (videoId) => {
    try {
      const response = await client.get(`/clips/${videoId}/generate`)
      return response
    } catch (error) { 
      console.error('Failed to generate clips:', error)
      throw error
    }
  }

  return {
    // 状态
    clips,
    currentClip,
    
    // 操作
    fetchClips,
    fetchClip,
    generateClips
  }
})
