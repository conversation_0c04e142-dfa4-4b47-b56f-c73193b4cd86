import { createRouter, createWebHistory } from 'vue-router'

// 页面组件懒加载
const TaskManagement = () => import('@/views/TaskManagement/TaskList.vue')
const VideoAnalysis = () => import('@/views/VideoAnalysis/AnalysisWorkspace.vue')
const ClipManagement = () => import('@/views/ClipManagement/ClipGrid.vue')
const EditingWorkspace = () => import('@/views/EditingWorkspace/Timeline.vue')

const routes = [
  {
    path: '/',
    redirect: '/tasks'
  },
  {
    path: '/tasks',
    name: 'TaskManagement',
    component: TaskManagement,
    meta: {
      title: '任务管理',
      requiresAuth: false
    }
  },
  {
    path: '/tasks/:id',
    name: 'TaskDetail',
    component: () => import('@/views/TaskManagement/TaskDetail.vue'),
    meta: {
      title: '任务详情',
      requiresAuth: false
    }
  },
  {
    path: '/analysis',
    name: 'VideoAnalysis',
    component: VideoAnalysis,
    meta: {
      title: '视频分析',
      requiresAuth: false
    }
  },
  {
    path: '/analysis/:videoId',
    name: 'VideoAnalysisDetail',
    component: () => import('@/views/VideoAnalysis/AnalysisDetail.vue'),
    meta: {
      title: '分析详情',
      requiresAuth: false
    }
  },
  {
    path: '/clips',
    name: 'ClipManagement',
    component: ClipManagement,
    meta: {
      title: '片段管理',
      requiresAuth: false
    }
  },
  {
    path: '/clips/:id',
    name: 'ClipDetail',
    component: () => import('@/views/ClipManagement/ClipDetail.vue'),
    meta: {
      title: '片段详情',
      requiresAuth: false
    }
  },
  {
    path: '/editor',
    name: 'EditingWorkspace',
    component: EditingWorkspace,
    meta: {
      title: '剪辑工作台',
      requiresAuth: false
    }
  },
  {
    path: '/editor/:projectId',
    name: 'ProjectEditor',
    component: () => import('@/views/EditingWorkspace/ProjectEditor.vue'),
    meta: {
      title: '项目编辑',
      requiresAuth: false
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 短剧分析剪辑`
  }
  
  // 这里可以添加认证检查
  if (to.meta.requiresAuth) {
    // 检查用户是否已登录
    // const isAuthenticated = checkAuth()
    // if (!isAuthenticated) {
    //   next('/login')
    //   return
    // }
  }
  
  next()
})

export default router
