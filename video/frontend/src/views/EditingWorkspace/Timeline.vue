<template>
  <div class="min-h-screen bg-gray-900 text-white">
    <!-- 顶部工具栏 -->
    <div class="bg-gray-800 border-b border-gray-700">
      <div class="flex items-center justify-between px-6 py-3">
        <div class="flex items-center space-x-4">
          <h1 class="text-xl font-semibold">时间轴编辑器</h1>
          <div class="flex items-center space-x-2">
            <button
              @click="goBack"
              class="text-gray-400 hover:text-white"
            >
              <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <span class="text-gray-400">|</span>
            <span class="text-sm text-gray-400">{{ currentProject?.name || '新项目' }}</span>
          </div>
        </div>
        
        <div class="flex items-center space-x-3">
          <!-- 播放控制 -->
          <div class="flex items-center space-x-2">
            <button
              @click="togglePlay"
              class="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded"
            >
              <svg v-if="!isPlaying" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
              </svg>
              <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </button>
            <button
              @click="stopPlayback"
              class="bg-gray-600 hover:bg-gray-700 text-white p-2 rounded"
            >
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v6a1 1 0 11-2 0V7zm4 0a1 1 0 012 0v6a1 1 0 11-2 0V7z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
          
          <!-- 时间显示 -->
          <div class="text-sm font-mono bg-gray-700 px-3 py-1 rounded">
            {{ formatTime(currentTime) }} / {{ formatTime(totalDuration) }}
          </div>
          
          <!-- 操作按钮 -->
          <div class="flex items-center space-x-2">
            <button
              @click="saveProject"
              class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm"
            >
              保存
            </button>
            <button
              @click="exportVideo"
              class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded text-sm"
            >
              导出
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="flex h-screen">
      <!-- 左侧素材库 -->
      <div class="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
        <div class="p-4 border-b border-gray-700">
          <h2 class="text-lg font-medium">素材库</h2>
        </div>
        
        <!-- 素材标签页 -->
        <div class="border-b border-gray-700">
          <nav class="flex">
            <button
              v-for="tab in materialTabs"
              :key="tab.id"
              @click="activeMaterialTab = tab.id"
              class="flex-1 py-3 px-4 text-sm font-medium border-b-2"
              :class="activeMaterialTab === tab.id 
                ? 'border-blue-500 text-blue-400' 
                : 'border-transparent text-gray-400 hover:text-gray-300'"
            >
              {{ tab.name }}
            </button>
          </nav>
        </div>
        
        <!-- 素材内容 -->
        <div class="flex-1 overflow-y-auto p-4">
          <!-- 视频片段 -->
          <div v-if="activeMaterialTab === 'clips'" class="space-y-3">
            <div
              v-for="clip in availableClips"
              :key="clip.id"
              class="bg-gray-700 rounded-lg p-3 cursor-pointer hover:bg-gray-600"
              draggable="true"
              @dragstart="startDrag($event, clip)"
            >
              <div class="flex items-center space-x-3">
                <div class="w-16 h-12 bg-gray-600 rounded flex-shrink-0">
                  <img
                    v-if="clip.thumbnail"
                    :src="clip.thumbnail"
                    :alt="clip.title"
                    class="w-full h-full object-cover rounded"
                  />
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium truncate">{{ clip.title }}</p>
                  <p class="text-xs text-gray-400">{{ formatTime(clip.duration) }}</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 转场效果 -->
          <div v-if="activeMaterialTab === 'transitions'" class="space-y-3">
            <div
              v-for="transition in availableTransitions"
              :key="transition.id"
              class="bg-gray-700 rounded-lg p-3 cursor-pointer hover:bg-gray-600"
              draggable="true"
              @dragstart="startDrag($event, transition)"
            >
              <div class="text-center">
                <div class="w-full h-8 bg-gradient-to-r from-gray-600 to-gray-500 rounded mb-2"></div>
                <p class="text-sm font-medium">{{ transition.name }}</p>
                <p class="text-xs text-gray-400">{{ transition.duration }}秒</p>
              </div>
            </div>
          </div>
          
          <!-- 音效 -->
          <div v-if="activeMaterialTab === 'audio'" class="space-y-3">
            <div
              v-for="audio in availableAudio"
              :key="audio.id"
              class="bg-gray-700 rounded-lg p-3 cursor-pointer hover:bg-gray-600"
              draggable="true"
              @dragstart="startDrag($event, audio)"
            >
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium">{{ audio.name }}</p>
                  <p class="text-xs text-gray-400">{{ formatTime(audio.duration) }}</p>
                </div>
                <button
                  @click="playAudio(audio)"
                  class="text-blue-400 hover:text-blue-300"
                >
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主编辑区域 -->
      <div class="flex-1 flex flex-col">
        <!-- 预览窗口 -->
        <div class="h-1/2 bg-black border-b border-gray-700 flex items-center justify-center">
          <div class="text-center">
            <svg class="mx-auto h-16 w-16 text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            <p class="text-gray-400">预览窗口</p>
            <p class="text-sm text-gray-500">拖拽片段到时间轴开始编辑</p>
          </div>
        </div>

        <!-- 时间轴区域 -->
        <div class="h-1/2 bg-gray-800 flex flex-col">
          <!-- 时间轴标尺 -->
          <div class="h-8 bg-gray-700 border-b border-gray-600 relative">
            <div class="flex items-center h-full px-4">
              <div class="flex-1 relative">
                <!-- 时间刻度 -->
                <div class="flex justify-between text-xs text-gray-400">
                  <span>0:00</span>
                  <span>0:30</span>
                  <span>1:00</span>
                  <span>1:30</span>
                  <span>2:00</span>
                </div>
                <!-- 播放头 -->
                <div
                  class="absolute top-0 w-0.5 h-full bg-red-500"
                  :style="{ left: `${(currentTime / totalDuration) * 100}%` }"
                ></div>
              </div>
            </div>
          </div>

          <!-- 轨道区域 -->
          <div class="flex-1 overflow-y-auto">
            <div class="p-4 space-y-2">
              <!-- 视频轨道 -->
              <div
                v-for="(track, trackIndex) in tracks"
                :key="track.id"
                class="flex items-center"
              >
                <!-- 轨道标签 -->
                <div class="w-20 text-sm text-gray-400 text-right pr-4">
                  {{ track.name }}
                </div>
                
                <!-- 轨道内容 -->
                <div
                  class="flex-1 h-16 bg-gray-700 rounded relative border-2 border-dashed border-gray-600"
                  @dragover.prevent
                  @drop="dropOnTrack($event, trackIndex)"
                >
                  <!-- 轨道上的片段 -->
                  <div
                    v-for="(segment, segmentIndex) in track.segments"
                    :key="segment.id"
                    class="absolute h-full bg-blue-600 rounded cursor-move flex items-center px-2"
                    :style="{ 
                      left: `${(segment.startTime / totalDuration) * 100}%`,
                      width: `${(segment.duration / totalDuration) * 100}%`
                    }"
                    @click="selectSegment(segment)"
                    @contextmenu.prevent="showSegmentMenu($event, segment, trackIndex, segmentIndex)"
                  >
                    <div class="text-xs text-white truncate">
                      {{ segment.title }}
                    </div>
                    
                    <!-- 调整手柄 -->
                    <div class="absolute left-0 top-0 w-2 h-full bg-blue-800 cursor-ew-resize"></div>
                    <div class="absolute right-0 top-0 w-2 h-full bg-blue-800 cursor-ew-resize"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="w-80 bg-gray-800 border-l border-gray-700 flex flex-col">
        <div class="p-4 border-b border-gray-700">
          <h2 class="text-lg font-medium">属性面板</h2>
        </div>
        
        <div class="flex-1 overflow-y-auto p-4">
          <!-- 选中片段属性 -->
          <div v-if="selectedSegment" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">片段标题</label>
              <input
                v-model="selectedSegment.title"
                type="text"
                class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:border-blue-500"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">开始时间</label>
              <input
                v-model="selectedSegment.startTime"
                type="number"
                step="0.1"
                class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:border-blue-500"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">持续时间</label>
              <input
                v-model="selectedSegment.duration"
                type="number"
                step="0.1"
                class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white focus:outline-none focus:border-blue-500"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-2">音量</label>
              <input
                v-model="selectedSegment.volume"
                type="range"
                min="0"
                max="100"
                class="w-full"
              />
              <div class="text-center text-sm text-gray-400">{{ selectedSegment.volume }}%</div>
            </div>
            
            <div class="space-y-2">
              <button
                @click="deleteSegment"
                class="w-full bg-red-600 hover:bg-red-700 text-white py-2 rounded text-sm"
              >
                删除片段
              </button>
              <button
                @click="duplicateSegment"
                class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded text-sm"
              >
                复制片段
              </button>
            </div>
          </div>
          
          <!-- 项目设置 -->
          <div v-else class="space-y-4">
            <div>
              <h3 class="text-sm font-medium text-gray-300 mb-2">项目设置</h3>
              <div class="space-y-3">
                <div>
                  <label class="block text-xs text-gray-400 mb-1">分辨率</label>
                  <select class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm">
                    <option>1920x1080</option>
                    <option>1280x720</option>
                    <option>3840x2160</option>
                  </select>
                </div>
                <div>
                  <label class="block text-xs text-gray-400 mb-1">帧率</label>
                  <select class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white text-sm">
                    <option>30 fps</option>
                    <option>24 fps</option>
                    <option>60 fps</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div
      v-if="contextMenu.show"
      class="fixed bg-gray-700 border border-gray-600 rounded shadow-lg py-2 z-50"
      :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
    >
      <button
        @click="cutSegment"
        class="block w-full text-left px-4 py-2 text-sm text-white hover:bg-gray-600"
      >
        剪切
      </button>
      <button
        @click="copySegment"
        class="block w-full text-left px-4 py-2 text-sm text-white hover:bg-gray-600"
      >
        复制
      </button>
      <button
        @click="deleteSegmentFromMenu"
        class="block w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-gray-600"
      >
        删除
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'

const router = useRouter()
const appStore = useAppStore()

// 响应式数据
const isPlaying = ref(false)
const currentTime = ref(0)
const totalDuration = ref(120) // 2分钟
const selectedSegment = ref(null)
const activeMaterialTab = ref('clips')
const currentProject = ref({ name: '新项目' })

// 素材标签页
const materialTabs = ref([
  { id: 'clips', name: '片段' },
  { id: 'transitions', name: '转场' },
  { id: 'audio', name: '音效' }
])

// 可用素材
const availableClips = ref([
  { id: 1, title: '开场对话', duration: 45.5, thumbnail: null },
  { id: 2, title: '动作场面', duration: 120.3, thumbnail: null },
  { id: 3, title: '情感高潮', duration: 68.2, thumbnail: null }
])

const availableTransitions = ref([
  { id: 1, name: '淡入淡出', duration: 1.0 },
  { id: 2, name: '切换', duration: 0.5 },
  { id: 3, name: '滑动', duration: 1.5 }
])

const availableAudio = ref([
  { id: 1, name: '背景音乐1', duration: 180 },
  { id: 2, name: '音效-爆炸', duration: 3.2 },
  { id: 3, name: '环境音-雨声', duration: 300 }
])

// 时间轴轨道
const tracks = ref([
  {
    id: 1,
    name: '视频1',
    type: 'video',
    segments: [
      {
        id: 1,
        title: '开场对话',
        startTime: 0,
        duration: 45.5,
        volume: 100
      }
    ]
  },
  {
    id: 2,
    name: '视频2',
    type: 'video',
    segments: []
  },
  {
    id: 3,
    name: '音频1',
    type: 'audio',
    segments: []
  }
])

// 右键菜单
const contextMenu = ref({
  show: false,
  x: 0,
  y: 0,
  segment: null,
  trackIndex: null,
  segmentIndex: null
})

// 方法
const goBack = () => {
  router.push('/editor')
}

const togglePlay = () => {
  isPlaying.value = !isPlaying.value
  if (isPlaying.value) {
    startPlayback()
  } else {
    pausePlayback()
  }
}

const stopPlayback = () => {
  isPlaying.value = false
  currentTime.value = 0
}

const startPlayback = () => {
  // 模拟播放
  const interval = setInterval(() => {
    if (!isPlaying.value) {
      clearInterval(interval)
      return
    }
    currentTime.value += 0.1
    if (currentTime.value >= totalDuration.value) {
      currentTime.value = totalDuration.value
      isPlaying.value = false
      clearInterval(interval)
    }
  }, 100)
}

const pausePlayback = () => {
  // 暂停逻辑
}

const startDrag = (event, item) => {
  event.dataTransfer.setData('application/json', JSON.stringify(item))
}

const dropOnTrack = (event, trackIndex) => {
  event.preventDefault()
  const item = JSON.parse(event.dataTransfer.getData('application/json'))
  
  // 添加到轨道
  const newSegment = {
    id: Date.now(),
    title: item.title || item.name,
    startTime: currentTime.value,
    duration: item.duration,
    volume: 100
  }
  
  tracks.value[trackIndex].segments.push(newSegment)
  appStore.showSuccess('添加成功', '片段已添加到时间轴')
}

const selectSegment = (segment) => {
  selectedSegment.value = segment
}

const showSegmentMenu = (event, segment, trackIndex, segmentIndex) => {
  contextMenu.value = {
    show: true,
    x: event.clientX,
    y: event.clientY,
    segment,
    trackIndex,
    segmentIndex
  }
}

const hideContextMenu = () => {
  contextMenu.value.show = false
}

const deleteSegment = () => {
  if (selectedSegment.value) {
    tracks.value.forEach(track => {
      const index = track.segments.findIndex(s => s.id === selectedSegment.value.id)
      if (index !== -1) {
        track.segments.splice(index, 1)
      }
    })
    selectedSegment.value = null
    appStore.showSuccess('删除成功', '片段已删除')
  }
}

const duplicateSegment = () => {
  if (selectedSegment.value) {
    const newSegment = {
      ...selectedSegment.value,
      id: Date.now(),
      startTime: selectedSegment.value.startTime + selectedSegment.value.duration
    }
    
    tracks.value.forEach(track => {
      if (track.segments.some(s => s.id === selectedSegment.value.id)) {
        track.segments.push(newSegment)
      }
    })
    appStore.showSuccess('复制成功', '片段已复制')
  }
}

const cutSegment = () => {
  // 剪切逻辑
  hideContextMenu()
}

const copySegment = () => {
  // 复制逻辑
  hideContextMenu()
}

const deleteSegmentFromMenu = () => {
  if (contextMenu.value.segment) {
    tracks.value[contextMenu.value.trackIndex].segments.splice(contextMenu.value.segmentIndex, 1)
    appStore.showSuccess('删除成功', '片段已删除')
  }
  hideContextMenu()
}

const saveProject = () => {
  appStore.showSuccess('保存成功', '项目已保存')
}

const exportVideo = () => {
  appStore.showSuccess('导出中', '正在导出视频...')
}

const playAudio = (audio) => {
  appStore.showInfo('播放', `正在播放: ${audio.name}`)
}

const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 生命周期
onMounted(() => {
  document.addEventListener('click', hideContextMenu)
})

onUnmounted(() => {
  document.removeEventListener('click', hideContextMenu)
})
</script>
