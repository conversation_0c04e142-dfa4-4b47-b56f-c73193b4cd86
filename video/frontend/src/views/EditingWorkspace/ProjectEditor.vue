<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-4">
            <button
              @click="goBack"
              class="text-gray-500 hover:text-gray-700"
            >
              <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <h1 class="text-3xl font-bold text-gray-900">项目编辑器</h1>
            <span class="text-gray-500">{{ project?.name || '加载中...' }}</span>
          </div>
          <div class="flex space-x-3">
            <button
              @click="saveProject"
              class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              保存项目
            </button>
            <button
              @click="openTimeline"
              class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              打开时间轴
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center min-h-96">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
    </div>

    <!-- 项目内容 -->
    <div v-else-if="project" class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 主要编辑区域 -->
        <div class="lg:col-span-2 space-y-6">
          <!-- 项目信息 -->
          <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">项目信息</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">项目名称</label>
                <input
                  v-model="project.name"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">分辨率</label>
                <select
                  v-model="project.resolution"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="1920x1080">1920x1080 (Full HD)</option>
                  <option value="1280x720">1280x720 (HD)</option>
                  <option value="3840x2160">3840x2160 (4K)</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">帧率</label>
                <select
                  v-model="project.framerate"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="24">24 fps</option>
                  <option value="30">30 fps</option>
                  <option value="60">60 fps</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">时长</label>
                <input
                  v-model="project.duration"
                  type="number"
                  step="0.1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">项目描述</label>
              <textarea
                v-model="project.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="输入项目描述..."
              ></textarea>
            </div>
          </div>

          <!-- 序列管理 -->
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-xl font-semibold text-gray-900">序列管理</h2>
              <button
                @click="addSequence"
                class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                添加序列
              </button>
            </div>
            
            <div class="space-y-3">
              <div
                v-for="(sequence, index) in project.sequences"
                :key="sequence.id"
                class="border border-gray-200 rounded-lg p-4 hover:border-gray-300"
              >
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <div class="flex items-center space-x-3 mb-2">
                      <input
                        v-model="sequence.name"
                        type="text"
                        class="text-lg font-medium bg-transparent border-none focus:outline-none focus:ring-0 p-0"
                        placeholder="序列名称"
                      />
                      <span class="text-sm text-gray-500">{{ formatDuration(sequence.duration) }}</span>
                    </div>
                    <p class="text-sm text-gray-600">{{ sequence.clips.length }} 个片段</p>
                  </div>
                  <div class="flex space-x-2">
                    <button
                      @click="editSequence(sequence)"
                      class="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      编辑
                    </button>
                    <button
                      @click="deleteSequence(index)"
                      class="text-red-600 hover:text-red-800 text-sm"
                    >
                      删除
                    </button>
                  </div>
                </div>
                
                <!-- 序列片段预览 -->
                <div class="mt-3">
                  <div class="flex space-x-2 overflow-x-auto">
                    <div
                      v-for="clip in sequence.clips"
                      :key="clip.id"
                      class="flex-shrink-0 w-24 h-16 bg-gray-200 rounded border"
                    >
                      <img
                        v-if="clip.thumbnail"
                        :src="clip.thumbnail"
                        :alt="clip.title"
                        class="w-full h-full object-cover rounded"
                      />
                      <div v-else class="w-full h-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </div>
                    </div>
                    <button
                      @click="addClipToSequence(sequence)"
                      class="flex-shrink-0 w-24 h-16 border-2 border-dashed border-gray-300 rounded flex items-center justify-center hover:border-gray-400"
                    >
                      <svg class="w-6 h-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 导出设置 -->
          <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">导出设置</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">输出格式</label>
                <select
                  v-model="project.export_settings.format"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="mp4">MP4</option>
                  <option value="mov">MOV</option>
                  <option value="avi">AVI</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">质量</label>
                <select
                  v-model="project.export_settings.quality"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="high">高质量</option>
                  <option value="medium">中等质量</option>
                  <option value="low">低质量</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">比特率</label>
                <input
                  v-model="project.export_settings.bitrate"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  placeholder="8000 kbps"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">音频编码</label>
                <select
                  v-model="project.export_settings.audio_codec"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="aac">AAC</option>
                  <option value="mp3">MP3</option>
                  <option value="wav">WAV</option>
                </select>
              </div>
            </div>
            
            <div class="mt-6 flex space-x-3">
              <button
                @click="previewExport"
                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                预览导出
              </button>
              <button
                @click="exportProject"
                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                开始导出
              </button>
            </div>
          </div>
        </div>

        <!-- 右侧面板 -->
        <div class="space-y-6">
          <!-- 项目统计 -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">项目统计</h3>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-gray-500">总时长</span>
                <span class="font-medium">{{ formatDuration(project.total_duration) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">序列数量</span>
                <span class="font-medium">{{ project.sequences.length }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">片段数量</span>
                <span class="font-medium">{{ getTotalClips() }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">创建时间</span>
                <span class="font-medium">{{ formatDate(project.created_at) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">最后修改</span>
                <span class="font-medium">{{ formatDate(project.updated_at) }}</span>
              </div>
            </div>
          </div>

          <!-- 最近操作 -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">最近操作</h3>
            <div class="space-y-3">
              <div
                v-for="action in recentActions"
                :key="action.id"
                class="flex items-start space-x-3"
              >
                <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm text-gray-900">{{ action.description }}</p>
                  <p class="text-xs text-gray-500">{{ formatDate(action.timestamp) }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 快捷操作 -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">快捷操作</h3>
            <div class="space-y-2">
              <button
                @click="autoArrange"
                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded"
              >
                自动排列片段
              </button>
              <button
                @click="addTransitions"
                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded"
              >
                批量添加转场
              </button>
              <button
                @click="adjustAudio"
                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded"
              >
                音频自动调节
              </button>
              <button
                @click="generatePreview"
                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded"
              >
                生成预览视频
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="flex items-center justify-center min-h-96">
      <div class="text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">项目未找到</h3>
        <p class="mt-1 text-sm text-gray-500">请检查项目ID是否正确</p>
        <div class="mt-6">
          <button
            @click="goBack"
            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            返回列表
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { format } from 'date-fns'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()

// 响应式数据
const loading = ref(true)
const project = ref(null)
const recentActions = ref([])

// 方法
const loadProject = async () => {
  try {
    const projectId = route.params.projectId
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟项目数据
    project.value = {
      id: parseInt(projectId),
      name: '短剧剪辑项目',
      description: '第一集的剪辑项目',
      resolution: '1920x1080',
      framerate: 30,
      duration: 120,
      total_duration: 180,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      sequences: [
        {
          id: 1,
          name: '主序列',
          duration: 120,
          clips: [
            { id: 1, title: '开场', thumbnail: null },
            { id: 2, title: '对话', thumbnail: null }
          ]
        }
      ],
      export_settings: {
        format: 'mp4',
        quality: 'high',
        bitrate: '8000 kbps',
        audio_codec: 'aac'
      }
    }
    
    // 模拟最近操作
    recentActions.value = [
      {
        id: 1,
        description: '添加了新的视频片段',
        timestamp: new Date().toISOString()
      },
      {
        id: 2,
        description: '调整了音频音量',
        timestamp: new Date(Date.now() - 300000).toISOString()
      }
    ]
  } catch (error) {
    appStore.showError('加载失败', '无法加载项目信息')
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push('/editor')
}

const saveProject = () => {
  appStore.showSuccess('保存成功', '项目已保存')
}

const openTimeline = () => {
  router.push('/editor')
}

const addSequence = () => {
  const newSequence = {
    id: Date.now(),
    name: `序列 ${project.value.sequences.length + 1}`,
    duration: 0,
    clips: []
  }
  project.value.sequences.push(newSequence)
  appStore.showSuccess('添加成功', '新序列已添加')
}

const editSequence = (sequence) => {
  appStore.showInfo('编辑', `正在编辑序列: ${sequence.name}`)
}

const deleteSequence = (index) => {
  project.value.sequences.splice(index, 1)
  appStore.showSuccess('删除成功', '序列已删除')
}

const addClipToSequence = (sequence) => {
  appStore.showInfo('添加片段', '请从素材库选择片段')
}

const previewExport = () => {
  appStore.showInfo('预览', '正在生成预览...')
}

const exportProject = () => {
  appStore.showSuccess('导出中', '项目导出已开始')
}

const getTotalClips = () => {
  return project.value.sequences.reduce((total, seq) => total + seq.clips.length, 0)
}

const autoArrange = () => {
  appStore.showSuccess('排列完成', '片段已自动排列')
}

const addTransitions = () => {
  appStore.showSuccess('添加完成', '转场效果已批量添加')
}

const adjustAudio = () => {
  appStore.showSuccess('调节完成', '音频已自动调节')
}

const generatePreview = () => {
  appStore.showInfo('生成中', '正在生成预览视频...')
}

const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatDate = (dateString) => {
  return format(new Date(dateString), 'yyyy-MM-dd HH:mm')
}

// 生命周期
onMounted(() => {
  loadProject()
})
</script>
