<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center min-h-screen">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
    </div>

    <!-- 片段详情 -->
    <div v-else-if="clip" class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- 返回按钮 -->
      <div class="mb-6">
        <button
          @click="goBack"
          class="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
        >
          <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          返回片段列表
        </button>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 视频播放器 -->
        <div class="lg:col-span-2">
          <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="aspect-video bg-black relative">
              <!-- 这里应该集成video.js播放器 -->
              <div class="w-full h-full flex items-center justify-center text-white">
                <div class="text-center">
                  <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4h10a2 2 0 012 2v8a2 2 0 01-2 2H6a2 2 0 01-2-2v-8a2 2 0 012-2z" />
                  </svg>
                  <p class="text-lg">视频播放器</p>
                  <p class="text-sm text-gray-400">{{ formatTimeRange(clip.start_time, clip.end_time) }}</p>
                </div>
              </div>
              
              <!-- 播放控制 -->
              <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
                <div class="flex items-center justify-between text-white">
                  <button class="hover:text-gray-300">
                    <svg class="h-8 w-8" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                    </svg>
                  </button>
                  <div class="flex-1 mx-4">
                    <div class="bg-gray-600 rounded-full h-1">
                      <div class="bg-white rounded-full h-1 w-1/3"></div>
                    </div>
                  </div>
                  <span class="text-sm">{{ formatDuration(clip.duration) }}</span>
                </div>
              </div>
            </div>
            
            <!-- 视频信息 -->
            <div class="p-6">
              <div class="flex justify-between items-start mb-4">
                <div>
                  <h1 class="text-2xl font-bold text-gray-900">{{ clip.title || '未命名片段' }}</h1>
                  <p class="mt-2 text-gray-600">{{ clip.description || '暂无描述' }}</p>
                </div>
                <div class="flex space-x-2">
                  <button
                    @click="downloadClip"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    下载
                  </button>
                  <button
                    @click="editClip"
                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    编辑
                  </button>
                </div>
              </div>
              
              <!-- 标签 -->
              <div v-if="clip.tags && clip.tags.length > 0" class="mb-4">
                <div class="flex flex-wrap gap-2">
                  <span
                    v-for="tag in clip.tags"
                    :key="tag"
                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                  >
                    {{ tag }}
                  </span>
                </div>
              </div>
              
              <!-- 基础信息 -->
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-gray-500">时长:</span>
                  <span class="ml-2 font-medium">{{ formatDuration(clip.duration) }}</span>
                </div>
                <div>
                  <span class="text-gray-500">类型:</span>
                  <span class="ml-2 font-medium">{{ getClipTypeText(clip.clip_type) }}</span>
                </div>
                <div>
                  <span class="text-gray-500">质量评分:</span>
                  <span class="ml-2 font-medium">{{ Math.round(clip.quality_score || 0) }}分</span>
                </div>
                <div>
                  <span class="text-gray-500">重要性:</span>
                  <span class="ml-2 font-medium">{{ Math.round(clip.importance_score || 0) }}分</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 侧边栏信息 -->
        <div class="space-y-6">
          <!-- 片段统计 -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">片段统计</h3>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-gray-500">开始时间</span>
                <span class="font-medium">{{ formatTime(clip.start_time) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">结束时间</span>
                <span class="font-medium">{{ formatTime(clip.end_time) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">创建时间</span>
                <span class="font-medium">{{ formatDate(clip.created_at) }}</span>
              </div>
            </div>
          </div>

          <!-- 分析结果 -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">AI分析结果</h3>
            <div v-if="clip.metadata" class="space-y-4">
              <!-- 人物信息 -->
              <div v-if="clip.metadata.characters">
                <h4 class="text-sm font-medium text-gray-700 mb-2">出现人物</h4>
                <div class="space-y-2">
                  <div
                    v-for="character in clip.metadata.characters"
                    :key="character.name"
                    class="flex items-center justify-between text-sm"
                  >
                    <span>{{ character.name }}</span>
                    <span class="text-gray-500">{{ Math.round(character.confidence * 100) }}%</span>
                  </div>
                </div>
              </div>
              
              <!-- 场景信息 -->
              <div v-if="clip.metadata.scene">
                <h4 class="text-sm font-medium text-gray-700 mb-2">场景分析</h4>
                <div class="text-sm text-gray-600">
                  <p>{{ clip.metadata.scene.description }}</p>
                  <p class="mt-1">
                    <span class="text-gray-500">类型:</span>
                    {{ clip.metadata.scene.type }}
                  </p>
                </div>
              </div>
              
              <!-- 情感分析 -->
              <div v-if="clip.metadata.emotion">
                <h4 class="text-sm font-medium text-gray-700 mb-2">情感分析</h4>
                <div class="space-y-2">
                  <div
                    v-for="emotion in clip.metadata.emotion"
                    :key="emotion.type"
                    class="flex items-center justify-between text-sm"
                  >
                    <span>{{ getEmotionText(emotion.type) }}</span>
                    <div class="flex items-center">
                      <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div
                          class="bg-blue-600 h-2 rounded-full"
                          :style="{ width: `${emotion.intensity * 100}%` }"
                        ></div>
                      </div>
                      <span class="text-gray-500">{{ Math.round(emotion.intensity * 100) }}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-sm text-gray-500">
              暂无分析数据
            </div>
          </div>

          <!-- 相关片段 -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">相关片段</h3>
            <div class="space-y-3">
              <div
                v-for="relatedClip in relatedClips"
                :key="relatedClip.id"
                class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer"
                @click="viewClip(relatedClip.id)"
              >
                <div class="w-16 h-12 bg-gray-200 rounded flex-shrink-0">
                  <img
                    v-if="relatedClip.thumbnail_path"
                    :src="relatedClip.thumbnail_path"
                    :alt="relatedClip.title"
                    class="w-full h-full object-cover rounded"
                  />
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    {{ relatedClip.title }}
                  </p>
                  <p class="text-xs text-gray-500">
                    {{ formatDuration(relatedClip.duration) }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">片段未找到</h3>
        <p class="mt-1 text-sm text-gray-500">请检查片段ID是否正确</p>
        <div class="mt-6">
          <button
            @click="goBack"
            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            返回列表
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { format } from 'date-fns'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()

// 响应式数据
const loading = ref(true)
const clip = ref(null)
const relatedClips = ref([])

// 方法
const loadClip = async () => {
  try {
    const clipId = route.params.id
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    clip.value = {
      id: parseInt(clipId),
      title: '开场对话片段',
      description: '主角与配角的重要开场对话，奠定了整个故事的基调',
      start_time: 120.5,
      end_time: 165.8,
      duration: 45.3,
      clip_type: 'dialogue',
      quality_score: 85,
      importance_score: 90,
      tags: ['对话', '开场', '重要', '情感'],
      created_at: new Date().toISOString(),
      metadata: {
        characters: [
          { name: '主角', confidence: 0.95 },
          { name: '配角A', confidence: 0.88 }
        ],
        scene: {
          type: '室内',
          description: '温馨的咖啡厅环境，光线柔和'
        },
        emotion: [
          { type: 'happy', intensity: 0.7 },
          { type: 'nervous', intensity: 0.3 }
        ]
      }
    }
    
    // 加载相关片段
    relatedClips.value = [
      {
        id: 2,
        title: '后续对话',
        duration: 32.1,
        thumbnail_path: null
      },
      {
        id: 3,
        title: '情感高潮',
        duration: 28.7,
        thumbnail_path: null
      }
    ]
  } catch (error) {
    appStore.showError('加载失败', '无法加载片段详情')
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push('/clips')
}

const viewClip = (clipId) => {
  router.push(`/clips/${clipId}`)
}

const downloadClip = () => {
  appStore.showSuccess('下载中', '正在准备下载文件...')
}

const editClip = () => {
  router.push(`/editor?clip=${clip.value.id}`)
}

const getClipTypeText = (type) => {
  const types = {
    character: '角色',
    scene: '场景',
    dialogue: '对话',
    action: '动作',
    emotion: '情感'
  }
  return types[type] || type
}

const getEmotionText = (emotion) => {
  const emotions = {
    happy: '快乐',
    sad: '悲伤',
    angry: '愤怒',
    nervous: '紧张',
    excited: '兴奋',
    calm: '平静'
  }
  return emotions[emotion] || emotion
}

const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatTimeRange = (start, end) => {
  return `${formatTime(start)} - ${formatTime(end)}`
}

const formatDate = (dateString) => {
  return format(new Date(dateString), 'yyyy-MM-dd HH:mm')
}

// 生命周期
onMounted(() => {
  loadClip()
})
</script>
