{"name": "short-drama-analysis-frontend", "version": "1.0.0", "description": "短剧视频分析剪辑软件前端", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.0", "video.js": "^8.6.1", "chart.js": "^4.4.0", "vue-chartjs": "^5.3.0", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "vue-upload-component": "^3.1.4", "sortablejs": "^1.15.0", "vuedraggable": "^4.1.0", "date-fns": "^2.30.0", "lodash-es": "^4.17.21"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@types/node": "^20.10.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7"}}