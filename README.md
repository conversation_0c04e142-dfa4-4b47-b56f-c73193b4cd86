# OmniParse - 多模态内容解析平台

## 项目简介

OmniParse 是一个基于 FastAPI 构建的多模态内容解析平台，集成了多种 AI 能力，支持：

### 📄 PDF 解析 (基于 MinerU)
- 文档布局分析
- 文本识别 (OCR)
- 表格提取
- 公式识别
- 图片内容分析

### 🎵 音频解析 (基于 ModelScope)
- 语音识别
- 音频转文本
- 支持多种音频格式

### 🤖 AIGC 检测
- AI 生成内容检测
- 基于深度学习模型
- 支持文本内容真实性评估

### 🎬 视频分析 (短剧制作工具)
- 智能视频分析
- 自动化剪辑
- 剧情片段提取
- 视频素材管理

## 技术架构

- **后端框架**: FastAPI
- **AI 引擎**:
  - MinerU (PDF解析)
  - ModelScope (音频处理)
  - HuggingFace Transformers (AIGC检测)
- **GPU 加速**: NVIDIA CUDA 支持
- **容器化**: Docker 部署
- **前端**: Vue.js + TailwindCSS (视频分析模块)

## 快速开始

### 环境配置

创建 `.env` 文件配置功能模块：

```bash
# 启用/禁用功能模块
ENABLE_PARSE_PDF=true      # PDF解析功能
ENABLE_PARSE_AUDIO=true    # 音频解析功能
ENABLE_DETECT_AIGC=true    # AIGC检测功能

# 服务配置
UVICORN_WORKERS=1          # 工作进程数
```

### Docker 构建

```bash
# 基础构建
docker build -t omniparse .

# 使用代理构建
docker build --build-arg http_proxy=http://127.0.0.1:7890 \
             --build-arg https_proxy=http://127.0.0.1:7890 \
             -t omniparse .
```

### 启动服务

```bash
# 启动完整服务 (需要GPU支持)
docker run --rm -it --gpus=all \
  -v ./paddleocr:/root/.paddleocr \
  -p 8888:8888 \
  omniparse

# 仅CPU模式 (部分功能受限)
docker run --rm -it \
  -v ./paddleocr:/root/.paddleocr \
  -p 8888:8888 \
  omniparse
```

### 本地开发

```bash
# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
python app.py
```

## API 文档

服务启动后访问 Swagger UI：
- http://localhost:8888/docs
- http://127.0.0.1:8888/docs

### API 端点

- **PDF 解析**: `POST /v1/parse/pdf`
- **音频解析**: `POST /v1/parse/audio`
- **AIGC 检测**: `POST /v1/detect/aigc`

## 项目结构

```
OmniParse/
├── app.py                    # FastAPI 主应用
├── requirements.txt          # Python 依赖
├── Dockerfile               # Docker 构建文件
├── entrypoint.sh            # 容器启动脚本
├── .env                     # 环境变量配置
│
├── pdf/                     # PDF 解析模块
│   ├── parse_pdf.py         # PDF 解析核心逻辑
│   ├── magic-pdf.json       # MinerU 配置文件
│   └── 2412.10432v2.pdf     # 测试文件
│
├── audio/                   # 音频解析模块
│   └── parse_audio.py       # 音频处理核心逻辑
│
├── aigc/                    # AIGC 检测模块
│   └── detect_aigc.py       # AI 内容检测逻辑
│
├── video/                   # 视频分析模块
│   ├── backend/             # 后端服务
│   ├── frontend/            # 前端界面
│   ├── storage/             # 存储目录
│   ├── short_drama_analysis_prd.md  # 产品需求文档
│   └── system_architecture.md      # 系统架构文档
│
├── local_infer_ref/         # 本地推理参考数据
│   ├── expand/              # 扩展任务参考
│   ├── generate/            # 生成任务参考
│   ├── polish/              # 润色任务参考
│   └── rewrite/             # 重写任务参考
│
└── devops/                  # 运维脚本
    └── docker.sh            # Docker 辅助脚本
```

## 功能特性

### 模块化设计
- 通过环境变量控制功能模块启用/禁用
- 支持按需部署，降低资源消耗
- 独立的路由和依赖管理

### 高性能
- GPU 加速推理
- 多进程支持
- 异步处理
- 模型懒加载

### 易于扩展
- 标准化的 API 接口
- 插件式架构
- 丰富的配置选项

## 依赖说明

### 核心依赖
- `fastapi==0.115.11` - Web 框架
- `uvicorn==0.34.0` - ASGI 服务器
- `magic_pdf==1.2.2` - PDF 解析引擎
- `modelscope==1.24.0` - 音频处理
- `huggingface_hub==0.29.3` - 模型管理

### 系统要求
- Python 3.10+
- NVIDIA GPU (推荐，用于加速)
- Docker (推荐部署方式)

## 注意事项

1. **首次运行**: 会自动下载所需的 AI 模型，请确保网络连接正常
2. **GPU 支持**: PDF 解析和 AIGC 检测功能需要 GPU 加速以获得最佳性能
3. **存储空间**: 模型文件较大，请确保有足够的磁盘空间
4. **内存要求**: 建议至少 8GB RAM，GPU 显存建议 6GB+

## 许可证

本项目采用开源许可证，具体请查看 LICENSE 文件。

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目。

## 联系方式

如有问题或建议，请通过 GitHub Issues 联系我们。
