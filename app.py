from dotenv import load_dotenv  # Import dotenv
import uvicorn
from fastapi import FastAPI
import os  # Import os for environment variable access

# Load environment variables from .env file
load_dotenv()

# Initialize FastAPI app
app = FastAPI()

# Conditionally include routers based on environment variables
if os.getenv("ENABLE_PARSE_PDF", "false").lower() == "true":
    from pdf.parse_pdf import router as pdf_router    
    app.include_router(pdf_router, prefix="/v1/parse/pdf")

if os.getenv("ENABLE_PARSE_AUDIO", "false").lower() == "true":
    from audio.parse_audio import router as audio_router
    app.include_router(audio_router, prefix="/v1/parse/audio")

if os.getenv("ENABLE_DETECT_AIGC", "false").lower() == "true":
    from aigc.detect_aigc import router as detect_aigc_router
    app.include_router(detect_aigc_router, prefix="/v1/detect/aigc")

# Main entry point
if __name__ == "__main__":
    workers = int(os.getenv("UVICORN_WORKERS", 1))  # Default to 1 worker if not set
    uvicorn.run(app, host="0.0.0.0", port=8888, workers=workers)
