# 导入必要的库
import glob  # 用于文件路径匹配
import json  # 处理JSON数据
import numpy as np  # 数值计算
import torch  # PyTorch深度学习框架
import os  # 操作系统接口
from transformers import AutoTokenizer  # 自动加载tokenizer
from peft import AutoPeftModelForCausalLM  # PEFT模型加载
from fastapi import HTTPException, APIRouter  # FastAPI异常处理和路由
from pydantic import BaseModel  # 数据验证模型
from fastapi.responses import JSONResponse  # JSON响应
from huggingface_hub import snapshot_download  # 从HuggingFace下载模型
from loguru import logger  # 日志记录


# Lock for thread-safe initialization


def checkAIGCEnabled():
    """
    检查是否启用了AIGC检测功能
    通过环境变量ENABLE_DETECT_AIGC控制，默认禁用
    """
    if os.getenv("ENABLE_DETECT_AIGC", "false").lower() != "true":
        logger.warning(
            "AIGC detection is disabled. Set ENABLE_DETECT_AIGC=true to enable it.")
        # 跳过后续执行但不报错
        return


checkAIGCEnabled()

_model_initialized = False  # Flag to ensure the model is initialized only once


def initialize_model():
    """
    初始化模型和tokenizer
    使用双重检查锁模式确保只初始化一次
    从HuggingFace下载两个模型：
    1. ImBD-inference模型(用于评分)
    2. GPT-Neo 2.7B模型(作为参考模型)
    """
    global _model_initialized, scoring_model, scoring_tokenizer
    if _model_initialized:
        return  # 如果已初始化则跳过

    if _model_initialized:
        return  # 双重检查锁

    # 模型下载路径
    model_dir = "/root/oo/OmniParse/models/ImBD-inference"
    gpt_neo_dir = "/root/oo/OmniParse/models/gpt-neo-2.7B"
    
    # 从HuggingFace下载模型
    snapshot_download(
        "xyzhu1225/ImBD-inference",
        local_dir=model_dir,
        # resume_download=True,
    )
    snapshot_download(
        "EleutherAI/gpt-neo-2.7B",
        local_dir=gpt_neo_dir,
        # resume_download=True,
        ignore_patterns=["*.bin", "*.ot", "*.msgpack"],
    )
    
    # 加载模型和tokenizer
    scoring_model = AutoPeftModelForCausalLM.from_pretrained(model_dir)
    scoring_tokenizer = AutoTokenizer.from_pretrained(model_dir)
    scoring_tokenizer.pad_token = scoring_tokenizer.eos_token  # 设置pad token
    scoring_model.to("cuda")  # 移动到GPU
    scoring_model.eval()  # 设置为评估模式
    _model_initialized = True
    logger.info("Model initialization successful")


# Ensure the model is initialized during startup
initialize_model()


class TextInput(BaseModel):
    text: str


def get_sampling_discrepancy_analytic(logits_ref, logits_score, labels):

    if logits_ref.size(-1) != logits_score.size(-1):
        vocab_size = min(logits_ref.size(-1), logits_score.size(-1))
        logits_ref = logits_ref[:, :, :vocab_size]
        logits_score = logits_score[:, :, :vocab_size]

    labels = labels.unsqueeze(-1) if labels.ndim == logits_score.ndim - \
        1 else labels
    lprobs_score = torch.log_softmax(logits_score, dim=-1)
    probs_ref = torch.softmax(logits_ref, dim=-1)

    log_likelihood = lprobs_score.gather(dim=-1, index=labels).squeeze(-1)
    mean_ref = (probs_ref * lprobs_score).sum(dim=-1)
    var_ref = (probs_ref * torch.square(lprobs_score)
               ).sum(dim=-1) - torch.square(mean_ref)
    discrepancy = (log_likelihood.sum(dim=-1) -
                   mean_ref.sum(dim=-1)) / var_ref.sum(dim=-1).sqrt()

    return discrepancy, log_likelihood.sum(dim=-1)


class ProbEstimator:
    def __init__(self):
        self.tasks = ["polish", "generate", "rewrite", "expand"]
        self.real_crits = {"polish": [],
                           "generate": [], "rewrite": [], "expand": []}
        self.fake_crits = {"polish": [],
                           "generate": [], "rewrite": [], "expand": []}
        for task in self.tasks:
            for result_file in glob.glob(os.path.join("/root/oo/OmniParse/local_infer_ref", task, '*.json')):
                with open(result_file, 'r') as fin:
                    res = json.load(fin)
                    self.real_crits[task].extend(res['crit_real'])
                    self.fake_crits[task].extend(res['crit_sampled'])
        print(f'ProbEstimator: total {sum([len(self.real_crits[task]) for task in self.tasks]) * 2} samples.')

    def crit_to_prob(self, crit):
        real_crits = []
        fake_crits = []
        for task in self.tasks:
            real_crits.extend(self.real_crits[task])
            fake_crits.extend(self.fake_crits[task])
        offset = np.sort(np.abs(np.array(real_crits + fake_crits) - crit))[100]
        cnt_real = np.sum((np.array(real_crits) > crit - offset)
                          & (np.array(real_crits) < crit + offset))
        cnt_fake = np.sum((np.array(fake_crits) > crit - offset)
                          & (np.array(fake_crits) < crit + offset))
        return cnt_fake / (cnt_real + cnt_fake)

    def crit_to_prob_detail(self, crit):
        probs = []
        for task in self.tasks:
            real_crits = self.real_crits[task]
            fake_crits = self.fake_crits[task]
            offset = np.sort(
                np.abs(np.array(real_crits + fake_crits) - crit))[100]
            cnt_real = np.sum((np.array(real_crits) > crit - offset)
                              & (np.array(real_crits) < crit + offset))
            cnt_fake = np.sum((np.array(fake_crits) > crit - offset)
                              & (np.array(fake_crits) < crit + offset))
            probs.append(cnt_fake / (cnt_real + cnt_fake))
        return probs


# Create a router for AIGC detection
router = APIRouter()


@router.post("", tags=["detect"], summary="Detect if text is AI-generated")
async def detect_aigc(input_data: TextInput):
    """
    Detect if the input text is AI-generated and return the probability for each task.
    """
    logger.info("Starting AIGC detection for text length: {}", len(input_data.text))
    
    if not _model_initialized:
        logger.error("Model not initialized properly")
        raise HTTPException(status_code=500, detail="Model not initialized")
        
    criterion_fn = get_sampling_discrepancy_analytic
    prob_estimator = ProbEstimator()
    try:
        # evaluate text
        logger.debug("Tokenizing input text")
        tokenized = scoring_tokenizer(input_data.text, truncation=True,
                                    return_tensors="pt", padding=True, 
                                    return_token_type_ids=False).to("cuda")
        labels = tokenized.input_ids[:, 1:]
        
        logger.debug("Running model inference")
        with torch.no_grad():
            logits_score = scoring_model(**tokenized).logits[:, :-1]
            logits_ref = logits_score
            crit, _ = criterion_fn(logits_ref, logits_score, labels)
        
        # estimate the probability of machine generated text
        crit = crit.cpu().numpy().item()
        logger.info("Calculated criterion value: {}", crit)
        
        probs = prob_estimator.crit_to_prob_detail(crit)
        probabilities = []
        for task, prob in zip(prob_estimator.tasks, probs):
            probabilities.append({
                "task_name": task,
                "probability": round(prob * 100, 2)
            })
            logger.info("Task {}: {}% probability", task, round(prob * 100, 2))

        result = {
            "criterion": round(crit, 4),
            "probabilities": probabilities,
        }
        logger.info("AIGC detection completed successfully")
        return JSONResponse(content=result, status_code=200)

    except torch.cuda.OutOfMemoryError as e:
        logger.error("CUDA out of memory error: {}", str(e))
        raise HTTPException(status_code=500, detail="GPU memory exceeded")
    except Exception as e:
        logger.exception("Error during AIGC detection: {}", str(e))
        raise HTTPException(
            status_code=500, detail=f"Error processing text: {str(e)}")
